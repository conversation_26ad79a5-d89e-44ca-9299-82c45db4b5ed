import React, { useState, useEffect } from 'react'
import { Helm<PERSON> } from 'react-helmet-async'
import { useQuery } from '@tanstack/react-query'
import {
  UsersIcon,
  ClipboardDocumentListIcon,
  CurrencyDollarIcon,
  ChartBarIcon,
  ArrowUpIcon,
  ArrowDownIcon,
} from '@heroicons/react/24/outline'
import { apiService, endpoints } from '@services/apiService'
import { StatsCard } from '@components/dashboard/StatsCard'
import { RevenueChart } from '@components/dashboard/RevenueChart'
import { TasksChart } from '@components/dashboard/TasksChart'
import { RecentActivity } from '@components/dashboard/RecentActivity'
import { TopWorkBoys } from '@components/dashboard/TopWorkBoys'
import { LoadingSpinner } from '@components/ui/LoadingSpinner'
import { ErrorMessage } from '@components/ui/ErrorMessage'

const DashboardPage = () => {
  const [dateRange, setDateRange] = useState('7d') // 7d, 30d, 90d, 1y

  const {
    data: dashboardData,
    isLoading,
    error,
    refetch,
  } = useQuery({
    queryKey: ['dashboard', dateRange],
    queryFn: () => apiService.get(`${endpoints.admin.dashboard}?range=${dateRange}`),
    refetchInterval: 30000, // Refetch every 30 seconds
  })

  if (isLoading) {
    return (
      <div className="flex h-96 items-center justify-center">
        <LoadingSpinner size="lg" />
      </div>
    )
  }

  if (error) {
    return (
      <div className="p-6">
        <ErrorMessage
          title="Failed to load dashboard"
          message="There was an error loading the dashboard data."
          onRetry={refetch}
        />
      </div>
    )
  }

  const stats = dashboardData?.stats || {}
  const charts = dashboardData?.charts || {}

  return (
    <>
      <Helmet>
        <title>Dashboard - WorkBoy Admin</title>
      </Helmet>

      <div className="px-4 sm:px-6 lg:px-8">
        {/* Header */}
        <div className="mb-8">
          <h1 className="text-2xl font-bold text-gray-900">Dashboard</h1>
          <p className="mt-1 text-sm text-gray-500">
            Welcome back! Here's what's happening with your platform.
          </p>
        </div>

        {/* Date Range Filter */}
        <div className="mb-6 flex justify-end">
          <select
            value={dateRange}
            onChange={(e) => setDateRange(e.target.value)}
            className="input w-auto"
          >
            <option value="7d">Last 7 days</option>
            <option value="30d">Last 30 days</option>
            <option value="90d">Last 90 days</option>
            <option value="1y">Last year</option>
          </select>
        </div>

        {/* Stats Cards */}
        <div className="mb-8 grid grid-cols-1 gap-6 sm:grid-cols-2 lg:grid-cols-4">
          <StatsCard
            title="Total Users"
            value={stats.total_users || 0}
            change={stats.users_change || 0}
            icon={UsersIcon}
            color="blue"
          />
          <StatsCard
            title="Active Tasks"
            value={stats.active_tasks || 0}
            change={stats.tasks_change || 0}
            icon={ClipboardDocumentListIcon}
            color="green"
          />
          <StatsCard
            title="Total Revenue"
            value={`₹${(stats.total_revenue || 0).toLocaleString()}`}
            change={stats.revenue_change || 0}
            icon={CurrencyDollarIcon}
            color="purple"
          />
          <StatsCard
            title="Work-Boys"
            value={stats.total_workboys || 0}
            change={stats.workboys_change || 0}
            icon={ChartBarIcon}
            color="orange"
          />
        </div>

        {/* Charts Row */}
        <div className="mb-8 grid grid-cols-1 gap-6 lg:grid-cols-2">
          {/* Revenue Chart */}
          <div className="card">
            <div className="card-header">
              <h3 className="text-lg font-medium text-gray-900">Revenue Overview</h3>
              <p className="text-sm text-gray-500">Daily revenue for the selected period</p>
            </div>
            <RevenueChart data={charts.revenue || []} />
          </div>

          {/* Tasks Chart */}
          <div className="card">
            <div className="card-header">
              <h3 className="text-lg font-medium text-gray-900">Task Statistics</h3>
              <p className="text-sm text-gray-500">Task completion trends</p>
            </div>
            <TasksChart data={charts.tasks || []} />
          </div>
        </div>

        {/* Bottom Row */}
        <div className="grid grid-cols-1 gap-6 lg:grid-cols-3">
          {/* Recent Activity */}
          <div className="lg:col-span-2">
            <div className="card">
              <div className="card-header">
                <h3 className="text-lg font-medium text-gray-900">Recent Activity</h3>
                <p className="text-sm text-gray-500">Latest platform activities</p>
              </div>
              <RecentActivity activities={dashboardData?.recent_activities || []} />
            </div>
          </div>

          {/* Top Work-Boys */}
          <div>
            <div className="card">
              <div className="card-header">
                <h3 className="text-lg font-medium text-gray-900">Top Work-Boys</h3>
                <p className="text-sm text-gray-500">Highest rated this month</p>
              </div>
              <TopWorkBoys workboys={dashboardData?.top_workboys || []} />
            </div>
          </div>
        </div>

        {/* Quick Actions */}
        <div className="mt-8">
          <div className="card">
            <div className="card-header">
              <h3 className="text-lg font-medium text-gray-900">Quick Actions</h3>
              <p className="text-sm text-gray-500">Common administrative tasks</p>
            </div>
            <div className="grid grid-cols-1 gap-4 sm:grid-cols-2 lg:grid-cols-4">
              <button className="btn btn-outline flex items-center justify-center p-4">
                <UsersIcon className="mr-2 h-5 w-5" />
                Manage Users
              </button>
              <button className="btn btn-outline flex items-center justify-center p-4">
                <ClipboardDocumentListIcon className="mr-2 h-5 w-5" />
                View Tasks
              </button>
              <button className="btn btn-outline flex items-center justify-center p-4">
                <CurrencyDollarIcon className="mr-2 h-5 w-5" />
                Financial Reports
              </button>
              <button className="btn btn-outline flex items-center justify-center p-4">
                <ChartBarIcon className="mr-2 h-5 w-5" />
                Analytics
              </button>
            </div>
          </div>
        </div>
      </div>
    </>
  )
}

export default DashboardPage
