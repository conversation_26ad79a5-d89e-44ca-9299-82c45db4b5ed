import React, { useState, useEffect } from 'react'
import { Helmet } from 'react-helmet-async'
import { useQuery } from '@tanstack/react-query'
import {
  UsersIcon,
  ClipboardDocumentListIcon,
  CurrencyDollarIcon,
  ChartBarIcon,
  ArrowUpIcon,
  ArrowDownIcon,
} from '@heroicons/react/24/outline'
import { dashboardService } from '@services/dashboardService'
import { StatsCard } from '@components/dashboard/StatsCard'
import { RevenueChart } from '@components/dashboard/RevenueChart'
import { TasksChart } from '@components/dashboard/TasksChart'
import { RecentActivity } from '@components/dashboard/RecentActivity'
import { TopWorkBoys } from '@components/dashboard/TopWorkBoys'
import { LoadingSpinner } from '@components/ui/LoadingSpinner'
import { ErrorMessage } from '@components/ui/ErrorMessage'

const DashboardPage = () => {
  const [dateRange, setDateRange] = useState('30d') // 7d, 30d, 90d, 1y

  // Fetch dashboard stats
  const {
    data: dashboardStats,
    isLoading: statsLoading,
    error: statsError,
    refetch: refetchStats
  } = useQuery({
    queryKey: ['dashboard-stats'],
    queryFn: dashboardService.getDashboardStats,
    refetchInterval: 5 * 60 * 1000, // Refetch every 5 minutes
  })

  // Fetch analytics data
  const {
    data: analyticsData,
    isLoading: analyticsLoading,
    error: analyticsError
  } = useQuery({
    queryKey: ['dashboard-analytics', dateRange],
    queryFn: () => dashboardService.getAnalytics(dateRange),
    refetchInterval: 10 * 60 * 1000, // Refetch every 10 minutes
  })

  // Fetch recent activities
  const {
    data: activitiesData,
    isLoading: activitiesLoading
  } = useQuery({
    queryKey: ['recent-activities'],
    queryFn: () => dashboardService.getRecentActivities(10),
    refetchInterval: 2 * 60 * 1000, // Refetch every 2 minutes
  })

  // Fetch top WorkBoys
  const {
    data: topWorkBoysData,
    isLoading: workBoysLoading
  } = useQuery({
    queryKey: ['top-workboys'],
    queryFn: () => dashboardService.getTopWorkBoys(5),
    refetchInterval: 15 * 60 * 1000, // Refetch every 15 minutes
  })

  // Loading state
  const isLoading = statsLoading || analyticsLoading
  const hasError = statsError || analyticsError

  if (isLoading) {
    return (
      <div className="flex h-96 items-center justify-center">
        <LoadingSpinner size="lg" />
      </div>
    )
  }

  if (hasError) {
    return (
      <div className="p-6">
        <ErrorMessage
          title="Failed to load dashboard"
          message="There was an error loading the dashboard data."
          onRetry={() => {
            refetchStats()
          }}
        />
      </div>
    )
  }

  const stats = dashboardStats?.data || {}
  const analytics = analyticsData?.data || {}
  const activities = activitiesData?.data || []
  const topWorkBoys = topWorkBoysData?.data || []

  return (
    <>
      <Helmet>
        <title>Dashboard - WorkBoy Admin</title>
      </Helmet>

      <div className="px-4 sm:px-6 lg:px-8">
        {/* Header */}
        <div className="mb-8">
          <h1 className="text-2xl font-bold text-gray-900">Dashboard</h1>
          <p className="mt-1 text-sm text-gray-500">
            Welcome back! Here's what's happening with your platform.
          </p>
        </div>

        {/* Date Range Filter */}
        <div className="mb-6 flex justify-end">
          <select
            value={dateRange}
            onChange={(e) => setDateRange(e.target.value)}
            className="input w-auto"
          >
            <option value="7d">Last 7 days</option>
            <option value="30d">Last 30 days</option>
            <option value="90d">Last 90 days</option>
            <option value="1y">Last year</option>
          </select>
        </div>

        {/* Stats Cards */}
        <div className="mb-8 grid grid-cols-1 gap-6 sm:grid-cols-2 lg:grid-cols-4">
          <StatsCard
            title="Total Users"
            value={stats.totalUsers || 0}
            change={12.5}
            icon={UsersIcon}
            color="blue"
          />
          <StatsCard
            title="Active Tasks"
            value={stats.pendingTasks || 0}
            change={8.2}
            icon={ClipboardDocumentListIcon}
            color="green"
          />
          <StatsCard
            title="Total Revenue"
            value={`$${(stats.totalRevenue || 0).toLocaleString()}`}
            change={15.3}
            icon={CurrencyDollarIcon}
            color="purple"
          />
          <StatsCard
            title="Active WorkBoys"
            value={stats.activeWorkBoys || 0}
            change={5.7}
            icon={ChartBarIcon}
            color="orange"
          />
        </div>

        {/* Charts Row */}
        <div className="mb-8 grid grid-cols-1 gap-6 lg:grid-cols-2">
          {/* Revenue Chart */}
          <RevenueChart
            data={analytics.revenueData || []}
            loading={analyticsLoading}
          />

          {/* Tasks Chart */}
          <TasksChart
            data={analytics.tasksByCategory || []}
            loading={analyticsLoading}
          />
        </div>

        {/* Bottom Row */}
        <div className="grid grid-cols-1 gap-6 lg:grid-cols-3">
          {/* Recent Activity */}
          <div className="lg:col-span-2">
            <RecentActivity
              activities={activities}
              loading={activitiesLoading}
            />
          </div>

          {/* Top Work-Boys */}
          <div>
            <TopWorkBoys
              workboys={topWorkBoysData?.data || []}
              loading={workBoysLoading}
            />
          </div>
        </div>

        {/* Quick Actions */}
        <div className="mt-8">
          <div className="card">
            <div className="card-header">
              <h3 className="text-lg font-medium text-gray-900">Quick Actions</h3>
              <p className="text-sm text-gray-500">Common administrative tasks</p>
            </div>
            <div className="grid grid-cols-1 gap-4 sm:grid-cols-2 lg:grid-cols-4">
              <button className="btn btn-outline flex items-center justify-center p-4">
                <UsersIcon className="mr-2 h-5 w-5" />
                Manage Users
              </button>
              <button className="btn btn-outline flex items-center justify-center p-4">
                <ClipboardDocumentListIcon className="mr-2 h-5 w-5" />
                View Tasks
              </button>
              <button className="btn btn-outline flex items-center justify-center p-4">
                <CurrencyDollarIcon className="mr-2 h-5 w-5" />
                Financial Reports
              </button>
              <button className="btn btn-outline flex items-center justify-center p-4">
                <ChartBarIcon className="mr-2 h-5 w-5" />
                Analytics
              </button>
            </div>
          </div>
        </div>
      </div>
    </>
  )
}

export default DashboardPage
