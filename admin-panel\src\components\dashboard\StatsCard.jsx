import React from 'react'
import { ArrowUpIcon, ArrowDownIcon, TrendingUpIcon } from '@heroicons/react/24/outline'
import { clsx } from 'clsx'

const colorClasses = {
  blue: {
    bg: 'bg-gradient-to-br from-blue-50 to-blue-100/50',
    text: 'text-blue-600',
    icon: 'bg-blue-500',
    glow: 'shadow-blue-500/20'
  },
  green: {
    bg: 'bg-gradient-to-br from-green-50 to-green-100/50',
    text: 'text-green-600',
    icon: 'bg-green-500',
    glow: 'shadow-green-500/20'
  },
  purple: {
    bg: 'bg-gradient-to-br from-purple-50 to-purple-100/50',
    text: 'text-purple-600',
    icon: 'bg-purple-500',
    glow: 'shadow-purple-500/20'
  },
  orange: {
    bg: 'bg-gradient-to-br from-orange-50 to-orange-100/50',
    text: 'text-orange-600',
    icon: 'bg-orange-500',
    glow: 'shadow-orange-500/20'
  },
  red: {
    bg: 'bg-gradient-to-br from-red-50 to-red-100/50',
    text: 'text-red-600',
    icon: 'bg-red-500',
    glow: 'shadow-red-500/20'
  },
}

export const StatsCard = ({
  title,
  value,
  change,
  icon: Icon,
  color = 'blue',
  className = '',
  trend = [],
  loading = false,
}) => {
  const isPositive = change >= 0
  const currentColor = colorClasses[color]

  if (loading) {
    return (
      <div className={clsx('bg-white rounded-xl border border-gray-200/60 p-6 shadow-card animate-pulse', className)}>
        <div className="flex items-center justify-between">
          <div className="flex-1">
            <div className="h-4 bg-gray-200 rounded w-24 mb-3"></div>
            <div className="h-8 bg-gray-200 rounded w-32 mb-3"></div>
            <div className="h-4 bg-gray-200 rounded w-20"></div>
          </div>
          <div className="w-12 h-12 bg-gray-200 rounded-xl"></div>
        </div>
      </div>
    )
  }

  return (
    <div className={clsx(
      'bg-white rounded-xl border border-gray-200/60 p-6 shadow-card hover:shadow-card-hover transition-all duration-300 group relative overflow-hidden',
      className
    )}>
      {/* Background gradient overlay */}
      <div className={clsx(
        'absolute inset-0 opacity-0 group-hover:opacity-100 transition-opacity duration-300',
        currentColor.bg
      )} />

      <div className="relative">
        <div className="flex items-center justify-between">
          <div className="flex-1">
            <p className="text-sm font-medium text-gray-600 mb-1">{title}</p>
            <p className="text-3xl font-bold text-gray-900 mb-3 tracking-tight">{value}</p>

            {change !== undefined && (
              <div className="flex items-center">
                <div className={clsx(
                  'flex items-center px-2 py-1 rounded-full text-xs font-medium',
                  isPositive
                    ? 'bg-green-100 text-green-700'
                    : 'bg-red-100 text-red-700'
                )}>
                  {isPositive ? (
                    <ArrowUpIcon className="h-3 w-3 mr-1" />
                  ) : (
                    <ArrowDownIcon className="h-3 w-3 mr-1" />
                  )}
                  {Math.abs(change)}%
                </div>
                <span className="text-xs text-gray-500 ml-2">vs last month</span>
              </div>
            )}
          </div>

          {Icon && (
            <div className={clsx(
              'p-3 rounded-xl shadow-lg transition-all duration-300 group-hover:scale-110',
              currentColor.icon,
              currentColor.glow
            )}>
              <Icon className="h-6 w-6 text-white" />
            </div>
          )}
        </div>

        {/* Mini trend chart */}
        {trend.length > 0 && (
          <div className="mt-4 flex items-end space-x-1 h-8">
            {trend.map((point, index) => (
              <div
                key={index}
                className={clsx(
                  'bg-gradient-to-t rounded-sm transition-all duration-300 group-hover:opacity-80',
                  currentColor.text.replace('text-', 'from-').replace('-600', '-400'),
                  currentColor.text.replace('text-', 'to-').replace('-600', '-600')
                )}
                style={{
                  height: `${(point / Math.max(...trend)) * 100}%`,
                  width: '4px'
                }}
              />
            ))}
          </div>
        )}
      </div>
    </div>
  )
}

export default StatsCard
