import React from 'react'
import { ArrowUpIcon, ArrowDownIcon } from '@heroicons/react/24/outline'
import { clsx } from 'clsx'

const colorClasses = {
  blue: {
    bg: 'bg-blue-50',
    icon: 'text-blue-600',
    iconBg: 'bg-blue-100',
  },
  green: {
    bg: 'bg-green-50',
    icon: 'text-green-600',
    iconBg: 'bg-green-100',
  },
  purple: {
    bg: 'bg-purple-50',
    icon: 'text-purple-600',
    iconBg: 'bg-purple-100',
  },
  orange: {
    bg: 'bg-orange-50',
    icon: 'text-orange-600',
    iconBg: 'bg-orange-100',
  },
  red: {
    bg: 'bg-red-50',
    icon: 'text-red-600',
    iconBg: 'bg-red-100',
  },
}

export const StatsCard = ({ title, value, change, icon: Icon, color = 'blue' }) => {
  const isPositive = change >= 0
  const colors = colorClasses[color]

  return (
    <div className={clsx('card', colors.bg)}>
      <div className="flex items-center">
        <div className="flex-shrink-0">
          <div className={clsx('flex h-12 w-12 items-center justify-center rounded-lg', colors.iconBg)}>
            <Icon className={clsx('h-6 w-6', colors.icon)} />
          </div>
        </div>
        <div className="ml-4 flex-1">
          <p className="text-sm font-medium text-gray-600">{title}</p>
          <p className="text-2xl font-bold text-gray-900">{value}</p>
          {change !== undefined && (
            <div className="flex items-center">
              {isPositive ? (
                <ArrowUpIcon className="h-4 w-4 text-green-500" />
              ) : (
                <ArrowDownIcon className="h-4 w-4 text-red-500" />
              )}
              <span
                className={clsx(
                  'ml-1 text-sm font-medium',
                  isPositive ? 'text-green-600' : 'text-red-600'
                )}
              >
                {Math.abs(change)}%
              </span>
              <span className="ml-1 text-sm text-gray-500">vs last period</span>
            </div>
          )}
        </div>
      </div>
    </div>
  )
}
