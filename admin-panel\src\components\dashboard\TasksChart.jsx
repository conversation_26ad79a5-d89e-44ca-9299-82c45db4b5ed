import React from 'react'
import {
  <PERSON><PERSON><PERSON>,
  <PERSON>,
  <PERSON>Axi<PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  CartesianGrid,
  Tooltip,
  ResponsiveContainer,
  Legend,
} from 'recharts'
import { format } from 'date-fns'

export const TasksChart = ({ data }) => {
  const formatTooltipValue = (value, name) => {
    const nameMap = {
      completed: 'Completed',
      pending: 'Pending',
      cancelled: 'Cancelled',
    }
    return [value, nameMap[name] || name]
  }

  const formatXAxisLabel = (tickItem) => {
    return format(new Date(tickItem), 'MMM dd')
  }

  return (
    <div className="h-80">
      <ResponsiveContainer width="100%" height="100%">
        <BarChart data={data} margin={{ top: 5, right: 30, left: 20, bottom: 5 }}>
          <CartesianGrid strokeDasharray="3 3" stroke="#f0f0f0" />
          <XAxis
            dataKey="date"
            tickFormatter={formatXAxisLabel}
            stroke="#6b7280"
            fontSize={12}
          />
          <YAxis stroke="#6b7280" fontSize={12} />
          <Tooltip
            formatter={formatTooltipValue}
            labelFormatter={(label) => format(new Date(label), 'MMM dd, yyyy')}
            contentStyle={{
              backgroundColor: '#fff',
              border: '1px solid #e5e7eb',
              borderRadius: '8px',
              boxShadow: '0 4px 6px -1px rgba(0, 0, 0, 0.1)',
            }}
          />
          <Legend />
          <Bar
            dataKey="completed"
            fill="#22c55e"
            name="Completed"
            radius={[2, 2, 0, 0]}
          />
          <Bar
            dataKey="pending"
            fill="#f59e0b"
            name="Pending"
            radius={[2, 2, 0, 0]}
          />
          <Bar
            dataKey="cancelled"
            fill="#ef4444"
            name="Cancelled"
            radius={[2, 2, 0, 0]}
          />
        </BarChart>
      </ResponsiveContainer>
    </div>
  )
}
