import React from 'react'
import { <PERSON>, CardHeader, CardTitle, CardDescription, CardContent } from '@components/ui/Card'
import { LoadingSpinner } from '@components/ui/LoadingSpinner'
import { clsx } from 'clsx'

const COLORS = [
  { from: 'from-blue-500', to: 'to-blue-600', bg: 'bg-blue-500' },
  { from: 'from-green-500', to: 'to-green-600', bg: 'bg-green-500' },
  { from: 'from-yellow-500', to: 'to-yellow-600', bg: 'bg-yellow-500' },
  { from: 'from-red-500', to: 'to-red-600', bg: 'bg-red-500' },
  { from: 'from-purple-500', to: 'to-purple-600', bg: 'bg-purple-500' },
]

export const TasksChart = ({ data = [], loading = false, className = '' }) => {
  if (loading) {
    return (
      <Card className={clsx('h-80', className)} gradient>
        <CardHeader>
          <div className="h-6 bg-gray-200 rounded w-32 mb-2 animate-pulse"></div>
          <div className="h-4 bg-gray-200 rounded w-48 animate-pulse"></div>
        </CardHeader>
        <CardContent>
          <div className="flex items-center justify-center h-48">
            <LoadingSpinner size="lg" />
          </div>
        </CardContent>
      </Card>
    )
  }

  const total = data.reduce((sum, item) => sum + item.value, 0)

  return (
    <Card className={clsx('h-80', className)} hover gradient>
      <CardHeader>
        <CardTitle size="lg">Task Categories</CardTitle>
        <CardDescription>Distribution of tasks by category</CardDescription>
      </CardHeader>
      <CardContent>
        <div className="flex items-center justify-center h-48">
          {/* Donut Chart */}
          <div className="relative w-40 h-40">
            <svg className="w-full h-full transform -rotate-90" viewBox="0 0 100 100">
              {data.map((item, index) => {
                const percentage = (item.value / total) * 100
                const circumference = 2 * Math.PI * 40
                const strokeDasharray = circumference
                const strokeDashoffset = circumference - (percentage / 100) * circumference
                const rotation = data
                  .slice(0, index)
                  .reduce((acc, curr) => acc + (curr.value / total) * 360, 0)

                return (
                  <circle
                    key={item.name}
                    cx="50"
                    cy="50"
                    r="40"
                    fill="transparent"
                    stroke={COLORS[index % COLORS.length].bg.replace('bg-', '#')}
                    strokeWidth="8"
                    strokeDasharray={strokeDasharray}
                    strokeDashoffset={strokeDashoffset}
                    className="transition-all duration-500 hover:stroke-[10] cursor-pointer"
                    style={{
                      transform: `rotate(${rotation}deg)`,
                      transformOrigin: '50px 50px',
                      filter: 'drop-shadow(0 2px 4px rgba(0,0,0,0.1))'
                    }}
                  />
                )
              })}
            </svg>

            {/* Center text */}
            <div className="absolute inset-0 flex flex-col items-center justify-center">
              <div className="text-2xl font-bold text-gray-900">{total}</div>
              <div className="text-xs text-gray-500">Total Tasks</div>
            </div>
          </div>
        </div>

        {/* Legend */}
        <div className="grid grid-cols-2 gap-3 mt-4 pt-4 border-t border-gray-200/60">
          {data.map((item, index) => (
            <div key={item.name} className="flex items-center space-x-2 group cursor-pointer">
              <div className={clsx(
                'w-3 h-3 rounded-full transition-transform duration-200 group-hover:scale-125',
                COLORS[index % COLORS.length].bg
              )}></div>
              <div className="flex-1 min-w-0">
                <div className="text-sm font-medium text-gray-900 truncate group-hover:text-primary-600 transition-colors">
                  {item.name}
                </div>
                <div className="text-xs text-gray-500">
                  {item.count} ({((item.value / total) * 100).toFixed(1)}%)
                </div>
              </div>
            </div>
          ))}
        </div>
      </CardContent>
    </Card>
  )
}
