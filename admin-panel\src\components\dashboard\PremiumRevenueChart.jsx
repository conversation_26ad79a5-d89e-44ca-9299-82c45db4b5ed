import React, { useState } from 'react'
import { Card, CardHeader, CardTitle, CardDescription, CardContent } from '@components/ui/Card'

const mockData = [
  { month: 'Jan', revenue: 45000, target: 50000 },
  { month: 'Feb', revenue: 52000, target: 55000 },
  { month: 'Mar', revenue: 48000, target: 52000 },
  { month: 'Apr', revenue: 61000, target: 58000 },
  { month: 'May', revenue: 55000, target: 60000 },
  { month: 'Jun', revenue: 67000, target: 65000 },
  { month: 'Jul', revenue: 72000, target: 70000 },
  { month: 'Aug', revenue: 69000, target: 72000 },
  { month: 'Sep', revenue: 78000, target: 75000 },
  { month: 'Oct', revenue: 82000, target: 80000 },
  { month: 'Nov', revenue: 85000, target: 85000 },
  { month: 'Dec', revenue: 91000, target: 90000 },
]

export const PremiumRevenueChart = ({ loading = false }) => {
  const [hoveredIndex, setHoveredIndex] = useState(null)
  const [selectedPeriod, setSelectedPeriod] = useState('12M')

  const maxValue = Math.max(...mockData.map(d => Math.max(d.revenue, d.target)))

  if (loading) {
    return (
      <Card className="relative overflow-hidden bg-white/80 backdrop-blur-sm border-secondary-200/50 shadow-lg">
        <CardHeader className="pb-4">
          <div className="animate-pulse">
            <div className="h-6 bg-secondary-200 rounded-lg w-48 mb-2"></div>
            <div className="h-4 bg-secondary-200 rounded-lg w-64"></div>
          </div>
        </CardHeader>
        <CardContent>
          <div className="animate-pulse">
            <div className="flex justify-between items-end h-64 space-x-2">
              {[...Array(12)].map((_, i) => (
                <div key={i} className="flex-1 space-y-2">
                  <div className="h-32 bg-secondary-200 rounded-t-lg"></div>
                  <div className="h-24 bg-secondary-100 rounded-t-lg"></div>
                  <div className="h-4 bg-secondary-200 rounded w-8 mx-auto"></div>
                </div>
              ))}
            </div>
          </div>
        </CardContent>
      </Card>
    )
  }

  return (
    <Card className="relative overflow-hidden bg-white/80 backdrop-blur-sm border-secondary-200/50 shadow-lg hover:shadow-xl transition-all duration-300 group">
      {/* Background Pattern */}
      <div className="absolute inset-0 bg-gradient-to-br from-white via-white to-primary-50/20 opacity-60"></div>
      
      {/* Floating Orbs */}
      <div className="absolute -top-8 -right-8 h-32 w-32 bg-gradient-to-br from-primary-400/10 to-primary-600/10 rounded-full blur-2xl group-hover:scale-125 transition-transform duration-700"></div>
      
      <CardHeader className="relative pb-6">
        <div className="flex items-center justify-between">
          <div>
            <CardTitle className="text-2xl font-bold bg-gradient-to-r from-secondary-900 to-secondary-700 bg-clip-text text-transparent">
              Revenue Analytics
            </CardTitle>
            <CardDescription className="text-secondary-600 font-medium">
              Monthly revenue vs targets with growth trends
            </CardDescription>
          </div>
          
          {/* Period Selector */}
          <div className="flex items-center space-x-1 bg-secondary-100/50 rounded-xl p-1">
            {['3M', '6M', '12M'].map((period) => (
              <button
                key={period}
                onClick={() => setSelectedPeriod(period)}
                className={`px-3 py-1.5 text-sm font-semibold rounded-lg transition-all duration-200 ${
                  selectedPeriod === period
                    ? 'bg-white text-primary-600 shadow-sm'
                    : 'text-secondary-600 hover:text-secondary-900 hover:bg-white/50'
                }`}
              >
                {period}
              </button>
            ))}
          </div>
        </div>
      </CardHeader>

      <CardContent className="relative">
        {/* Chart */}
        <div className="relative">
          {/* Y-axis labels */}
          <div className="absolute left-0 top-0 h-64 flex flex-col justify-between text-xs text-secondary-500 font-medium">
            <span>${(maxValue / 1000).toFixed(0)}k</span>
            <span>${(maxValue * 0.75 / 1000).toFixed(0)}k</span>
            <span>${(maxValue * 0.5 / 1000).toFixed(0)}k</span>
            <span>${(maxValue * 0.25 / 1000).toFixed(0)}k</span>
            <span>$0</span>
          </div>

          {/* Chart Area */}
          <div className="ml-12 relative">
            {/* Grid Lines */}
            <div className="absolute inset-0 flex flex-col justify-between">
              {[...Array(5)].map((_, i) => (
                <div key={i} className="h-px bg-secondary-200/50"></div>
              ))}
            </div>

            {/* Bars */}
            <div className="flex justify-between items-end h-64 space-x-3 relative z-10">
              {mockData.map((item, index) => {
                const revenueHeight = (item.revenue / maxValue) * 100
                const targetHeight = (item.target / maxValue) * 100
                const isHovered = hoveredIndex === index

                return (
                  <div
                    key={item.month}
                    className="flex-1 flex flex-col items-center space-y-2 group/bar cursor-pointer"
                    onMouseEnter={() => setHoveredIndex(index)}
                    onMouseLeave={() => setHoveredIndex(null)}
                  >
                    {/* Tooltip */}
                    {isHovered && (
                      <div className="absolute -top-16 bg-secondary-900 text-white px-3 py-2 rounded-xl text-sm font-medium shadow-xl z-20 animate-fade-in-up">
                        <div className="text-center">
                          <div className="text-xs text-secondary-300">Revenue</div>
                          <div className="font-bold">${(item.revenue / 1000).toFixed(0)}k</div>
                          <div className="text-xs text-secondary-300 mt-1">Target: ${(item.target / 1000).toFixed(0)}k</div>
                        </div>
                        <div className="absolute top-full left-1/2 transform -translate-x-1/2 border-4 border-transparent border-t-secondary-900"></div>
                      </div>
                    )}

                    {/* Bars Container */}
                    <div className="relative flex space-x-1 items-end h-64">
                      {/* Target Bar (Background) */}
                      <div
                        className="w-6 bg-secondary-200 rounded-t-lg transition-all duration-500"
                        style={{ height: `${targetHeight}%` }}
                      />
                      
                      {/* Revenue Bar */}
                      <div
                        className={`w-6 bg-gradient-to-t from-primary-500 to-primary-400 rounded-t-lg shadow-lg transition-all duration-500 hover:shadow-xl hover:shadow-primary-500/25 ${
                          isHovered ? 'scale-110 shadow-xl shadow-primary-500/30' : ''
                        }`}
                        style={{ 
                          height: `${revenueHeight}%`,
                          animationDelay: `${index * 100}ms`
                        }}
                      />
                    </div>

                    {/* Month Label */}
                    <span className={`text-xs font-medium transition-colors duration-200 ${
                      isHovered ? 'text-primary-600' : 'text-secondary-500'
                    }`}>
                      {item.month}
                    </span>
                  </div>
                )
              })}
            </div>
          </div>
        </div>

        {/* Legend */}
        <div className="flex items-center justify-center space-x-6 mt-6 pt-4 border-t border-secondary-200/50">
          <div className="flex items-center space-x-2">
            <div className="w-3 h-3 bg-gradient-to-r from-primary-500 to-primary-400 rounded-full"></div>
            <span className="text-sm font-medium text-secondary-700">Revenue</span>
          </div>
          <div className="flex items-center space-x-2">
            <div className="w-3 h-3 bg-secondary-200 rounded-full"></div>
            <span className="text-sm font-medium text-secondary-700">Target</span>
          </div>
        </div>

        {/* Summary Stats */}
        <div className="grid grid-cols-3 gap-4 mt-6 pt-4 border-t border-secondary-200/50">
          <div className="text-center">
            <div className="text-2xl font-bold text-secondary-900">$847k</div>
            <div className="text-xs text-secondary-500 font-medium">Total Revenue</div>
          </div>
          <div className="text-center">
            <div className="text-2xl font-bold text-success-600">+12.5%</div>
            <div className="text-xs text-secondary-500 font-medium">Growth Rate</div>
          </div>
          <div className="text-center">
            <div className="text-2xl font-bold text-primary-600">94%</div>
            <div className="text-xs text-secondary-500 font-medium">Target Achievement</div>
          </div>
        </div>
      </CardContent>
    </Card>
  )
}
