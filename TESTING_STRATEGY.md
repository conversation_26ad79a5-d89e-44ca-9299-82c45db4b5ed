# WorkBoy Platform Testing Strategy

## Overview

This document outlines the comprehensive testing strategy for the WorkBoy platform, covering all applications and ensuring high-quality, reliable software delivery.

## Testing Pyramid

### 1. Unit Tests (70%)
- **Backend (PHP)**: PHPUnit tests for models, services, and utilities
- **Frontend (React.js)**: Jest + React Testing Library
- **Mobile (React Native)**: Jest + React Native Testing Library

### 2. Integration Tests (20%)
- API integration tests
- Database integration tests
- Third-party service integration tests
- WebSocket integration tests

### 3. End-to-End Tests (10%)
- User journey tests
- Cross-platform workflow tests
- Performance tests
- Security tests

## Backend Testing (PHP CodeIgniter 4)

### Unit Tests
```php
// Example: UserModelTest.php
class UserModelTest extends CIUnitTestCase
{
    use DatabaseTestTrait;

    public function testCreateUser()
    {
        $userModel = new UserModel();
        $userData = [
            'email' => '<EMAIL>',
            'password' => 'hashedpassword',
            'first_name' => 'Test',
            'last_name' => 'User',
            'user_type' => 'customer'
        ];
        
        $userId = $userModel->insert($userData);
        $this->assertIsNumeric($userId);
        
        $user = $userModel->find($userId);
        $this->assertEquals('<EMAIL>', $user['email']);
    }
}
```

### Integration Tests
```php
// Example: TaskWorkflowTest.php
class TaskWorkflowTest extends CIUnitTestCase
{
    public function testCompleteTaskWorkflow()
    {
        // Create customer and work-boy
        // Create task
        // Assign task
        // Complete task
        // Process payment
        // Verify all states
    }
}
```

### Test Commands
```bash
# Run all tests
php spark test

# Run specific test group
php spark test --group unit
php spark test --group integration

# Run with coverage
php spark test --coverage-html coverage/
```

## Frontend Testing (React.js)

### Unit Tests
```javascript
// Example: TaskCard.test.js
import { render, screen, fireEvent } from '@testing-library/react'
import TaskCard from '../TaskCard'

describe('TaskCard', () => {
  const mockTask = {
    id: 1,
    title: 'Test Task',
    description: 'Test Description',
    status: 'pending',
    amount: 500
  }

  test('renders task information correctly', () => {
    render(<TaskCard task={mockTask} />)
    
    expect(screen.getByText('Test Task')).toBeInTheDocument()
    expect(screen.getByText('Test Description')).toBeInTheDocument()
    expect(screen.getByText('₹500')).toBeInTheDocument()
  })

  test('calls onEdit when edit button is clicked', () => {
    const mockOnEdit = jest.fn()
    render(<TaskCard task={mockTask} onEdit={mockOnEdit} />)
    
    fireEvent.click(screen.getByText('Edit'))
    expect(mockOnEdit).toHaveBeenCalledWith(mockTask.id)
  })
})
```

### Integration Tests
```javascript
// Example: TaskManagement.integration.test.js
import { render, screen, waitFor } from '@testing-library/react'
import { QueryClient, QueryClientProvider } from '@tanstack/react-query'
import TaskManagement from '../TaskManagement'

describe('TaskManagement Integration', () => {
  test('loads and displays tasks from API', async () => {
    const queryClient = new QueryClient()
    
    render(
      <QueryClientProvider client={queryClient}>
        <TaskManagement />
      </QueryClientProvider>
    )

    await waitFor(() => {
      expect(screen.getByText('My Tasks')).toBeInTheDocument()
    })
  })
})
```

### Test Commands
```bash
# Run tests
npm test

# Run with coverage
npm test -- --coverage

# Run specific test file
npm test TaskCard.test.js

# Run in watch mode
npm test -- --watch
```

## Mobile Testing (React Native)

### Unit Tests
```javascript
// Example: TaskScreen.test.js
import React from 'react'
import { render, fireEvent } from '@testing-library/react-native'
import TaskScreen from '../TaskScreen'

describe('TaskScreen', () => {
  test('renders task list correctly', () => {
    const { getByText } = render(<TaskScreen />)
    expect(getByText('My Tasks')).toBeTruthy()
  })

  test('navigates to task detail on task press', () => {
    const mockNavigate = jest.fn()
    const { getByTestId } = render(
      <TaskScreen navigation={{ navigate: mockNavigate }} />
    )
    
    fireEvent.press(getByTestId('task-item-1'))
    expect(mockNavigate).toHaveBeenCalledWith('TaskDetail', { taskId: 1 })
  })
})
```

### E2E Tests (Detox)
```javascript
// Example: TaskFlow.e2e.js
describe('Task Flow', () => {
  beforeAll(async () => {
    await device.launchApp()
  })

  beforeEach(async () => {
    await device.reloadReactNative()
  })

  it('should create a new task', async () => {
    await element(by.id('create-task-button')).tap()
    await element(by.id('task-title-input')).typeText('Test Task')
    await element(by.id('task-description-input')).typeText('Test Description')
    await element(by.id('submit-button')).tap()
    
    await expect(element(by.text('Task created successfully'))).toBeVisible()
  })
})
```

### Test Commands
```bash
# Run unit tests
npm test

# Run E2E tests
npx detox test

# Run on specific platform
npx detox test --configuration ios.sim.debug
npx detox test --configuration android.emu.debug
```

## API Testing

### Postman Collections
- Authentication flows
- CRUD operations
- Error scenarios
- Performance tests

### Newman (CLI Testing)
```bash
# Run Postman collection
newman run WorkBoy_API_Collection.json \
  --environment WorkBoy_Environment.json \
  --reporters cli,html \
  --reporter-html-export results.html
```

### Load Testing (Artillery)
```yaml
# load-test.yml
config:
  target: 'http://localhost:8080'
  phases:
    - duration: 60
      arrivalRate: 10
scenarios:
  - name: "API Load Test"
    requests:
      - get:
          url: "/api/v1/tasks"
          headers:
            Authorization: "Bearer {{ token }}"
```

```bash
# Run load test
artillery run load-test.yml
```

## Security Testing

### OWASP ZAP
```bash
# Start ZAP proxy
zap.sh -daemon -port 8090

# Run security scan
zap-cli quick-scan http://localhost:3000
zap-cli spider http://localhost:3000
zap-cli active-scan http://localhost:3000
```

### Security Checklist
- [ ] SQL Injection protection
- [ ] XSS prevention
- [ ] CSRF protection
- [ ] Authentication bypass
- [ ] Authorization flaws
- [ ] Input validation
- [ ] File upload security
- [ ] API rate limiting

## Performance Testing

### Web Performance (Lighthouse)
```bash
# Run Lighthouse audit
lighthouse http://localhost:3000 \
  --output html \
  --output-path ./lighthouse-report.html
```

### Mobile Performance
- React Native Performance Monitor
- Flipper integration
- Memory leak detection
- Bundle size analysis

### Database Performance
```sql
-- Query performance analysis
EXPLAIN ANALYZE SELECT * FROM tasks 
WHERE status = 'pending' 
AND created_at > NOW() - INTERVAL 1 DAY;
```

## Continuous Integration

### GitHub Actions Workflow
```yaml
# .github/workflows/test.yml
name: Test Suite

on: [push, pull_request]

jobs:
  backend-tests:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v2
      - name: Setup PHP
        uses: shivammathur/setup-php@v2
        with:
          php-version: '8.1'
      - name: Install dependencies
        run: composer install
      - name: Run tests
        run: php spark test --coverage-clover coverage.xml

  frontend-tests:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v2
      - name: Setup Node.js
        uses: actions/setup-node@v2
        with:
          node-version: '18'
      - name: Install dependencies
        run: npm ci
      - name: Run tests
        run: npm test -- --coverage

  mobile-tests:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v2
      - name: Setup Node.js
        uses: actions/setup-node@v2
        with:
          node-version: '18'
      - name: Install dependencies
        run: npm ci
      - name: Run tests
        run: npm test
```

## Test Data Management

### Database Seeding
```php
// DatabaseSeeder.php
class DatabaseSeeder extends Seeder
{
    public function run()
    {
        $this->call('UserSeeder');
        $this->call('CategorySeeder');
        $this->call('TaskSeeder');
    }
}
```

### Test Fixtures
```javascript
// fixtures/tasks.js
export const mockTasks = [
  {
    id: 1,
    title: 'Cleaning Service',
    description: 'Deep cleaning required',
    status: 'pending',
    amount: 1000
  },
  // ... more test data
]
```

## Quality Gates

### Code Coverage Thresholds
- Backend: 80% minimum
- Frontend: 75% minimum
- Mobile: 70% minimum

### Performance Thresholds
- API response time: < 200ms (95th percentile)
- Web page load time: < 3 seconds
- Mobile app startup: < 2 seconds

### Security Requirements
- No high/critical vulnerabilities
- All dependencies up to date
- Security headers implemented

## Test Reporting

### Coverage Reports
- HTML coverage reports
- SonarQube integration
- Codecov integration

### Test Results
- JUnit XML format
- Slack notifications
- Email reports for failures

## Best Practices

### Test Writing
1. Follow AAA pattern (Arrange, Act, Assert)
2. Use descriptive test names
3. Keep tests independent
4. Mock external dependencies
5. Test edge cases and error scenarios

### Test Maintenance
1. Regular test review and cleanup
2. Update tests with feature changes
3. Remove obsolete tests
4. Refactor test code for maintainability

### Test Environment
1. Isolated test databases
2. Consistent test data
3. Environment parity
4. Fast test execution

## Monitoring and Alerting

### Test Metrics
- Test execution time
- Test failure rates
- Code coverage trends
- Performance regression detection

### Alerts
- Test failures in CI/CD
- Coverage drops below threshold
- Performance degradation
- Security vulnerability detection
