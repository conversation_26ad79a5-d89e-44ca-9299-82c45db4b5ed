import React, { useState } from 'react'
import { Card, CardHeader, CardTitle, CardDescription, CardContent } from '@components/ui/Card'

const mockData = [
  { name: 'Completed', value: 342, color: '#10b981', percentage: 45 },
  { name: 'In Progress', value: 156, color: '#3b82f6', percentage: 20 },
  { name: 'Pending', value: 189, color: '#f59e0b', percentage: 25 },
  { name: 'Cancelled', value: 78, color: '#ef4444', percentage: 10 },
]

export const PremiumTasksChart = ({ loading = false }) => {
  const [hoveredIndex, setHoveredIndex] = useState(null)
  
  const total = mockData.reduce((sum, item) => sum + item.value, 0)
  const radius = 80
  const strokeWidth = 16
  const normalizedRadius = radius - strokeWidth * 2
  const circumference = normalizedRadius * 2 * Math.PI

  if (loading) {
    return (
      <Card className="relative overflow-hidden bg-white/80 backdrop-blur-sm border-secondary-200/50 shadow-lg">
        <CardHeader className="pb-4">
          <div className="animate-pulse">
            <div className="h-6 bg-secondary-200 rounded-lg w-40 mb-2"></div>
            <div className="h-4 bg-secondary-200 rounded-lg w-56"></div>
          </div>
        </CardHeader>
        <CardContent>
          <div className="animate-pulse flex items-center justify-center">
            <div className="h-48 w-48 bg-secondary-200 rounded-full"></div>
          </div>
        </CardContent>
      </Card>
    )
  }

  let cumulativePercentage = 0

  return (
    <Card className="relative overflow-hidden bg-white/80 backdrop-blur-sm border-secondary-200/50 shadow-lg hover:shadow-xl transition-all duration-300 group">
      {/* Background Pattern */}
      <div className="absolute inset-0 bg-gradient-to-br from-white via-white to-emerald-50/20 opacity-60"></div>
      
      {/* Floating Orbs */}
      <div className="absolute -top-8 -left-8 h-32 w-32 bg-gradient-to-br from-emerald-400/10 to-emerald-600/10 rounded-full blur-2xl group-hover:scale-125 transition-transform duration-700"></div>
      
      <CardHeader className="relative pb-6">
        <CardTitle className="text-2xl font-bold bg-gradient-to-r from-secondary-900 to-secondary-700 bg-clip-text text-transparent">
          Task Distribution
        </CardTitle>
        <CardDescription className="text-secondary-600 font-medium">
          Overview of task statuses and completion rates
        </CardDescription>
      </CardHeader>

      <CardContent className="relative">
        <div className="flex items-center justify-between">
          {/* Chart */}
          <div className="relative flex items-center justify-center">
            <svg
              height={radius * 2}
              width={radius * 2}
              className="transform -rotate-90 drop-shadow-lg"
            >
              {/* Background Circle */}
              <circle
                stroke="#f1f5f9"
                fill="transparent"
                strokeWidth={strokeWidth}
                r={normalizedRadius}
                cx={radius}
                cy={radius}
                className="opacity-50"
              />
              
              {/* Data Segments */}
              {mockData.map((item, index) => {
                const strokeDasharray = `${item.percentage * circumference / 100} ${circumference}`
                const strokeDashoffset = -cumulativePercentage * circumference / 100
                const rotation = cumulativePercentage * 3.6
                cumulativePercentage += item.percentage
                
                const isHovered = hoveredIndex === index

                return (
                  <circle
                    key={item.name}
                    stroke={item.color}
                    fill="transparent"
                    strokeWidth={isHovered ? strokeWidth + 4 : strokeWidth}
                    strokeDasharray={strokeDasharray}
                    strokeDashoffset={strokeDashoffset}
                    r={normalizedRadius}
                    cx={radius}
                    cy={radius}
                    className={`transition-all duration-300 cursor-pointer ${
                      isHovered ? 'drop-shadow-xl' : 'drop-shadow-md'
                    }`}
                    style={{
                      filter: isHovered ? `drop-shadow(0 4px 8px ${item.color}40)` : 'none'
                    }}
                    onMouseEnter={() => setHoveredIndex(index)}
                    onMouseLeave={() => setHoveredIndex(null)}
                  />
                )
              })}
            </svg>
            
            {/* Center Content */}
            <div className="absolute inset-0 flex flex-col items-center justify-center">
              <div className="text-3xl font-bold text-secondary-900">{total}</div>
              <div className="text-sm text-secondary-500 font-medium">Total Tasks</div>
              {hoveredIndex !== null && (
                <div className="mt-2 text-center animate-fade-in">
                  <div className="text-lg font-bold" style={{ color: mockData[hoveredIndex].color }}>
                    {mockData[hoveredIndex].value}
                  </div>
                  <div className="text-xs text-secondary-500">
                    {mockData[hoveredIndex].name}
                  </div>
                </div>
              )}
            </div>
          </div>

          {/* Legend */}
          <div className="space-y-3 ml-8">
            {mockData.map((item, index) => {
              const isHovered = hoveredIndex === index
              
              return (
                <div
                  key={item.name}
                  className={`flex items-center space-x-3 p-3 rounded-xl cursor-pointer transition-all duration-200 ${
                    isHovered 
                      ? 'bg-secondary-50 scale-105 shadow-md' 
                      : 'hover:bg-secondary-50/50'
                  }`}
                  onMouseEnter={() => setHoveredIndex(index)}
                  onMouseLeave={() => setHoveredIndex(null)}
                >
                  <div
                    className={`w-4 h-4 rounded-full transition-all duration-200 ${
                      isHovered ? 'scale-125 shadow-lg' : ''
                    }`}
                    style={{ 
                      backgroundColor: item.color,
                      boxShadow: isHovered ? `0 4px 12px ${item.color}40` : 'none'
                    }}
                  />
                  <div className="flex-1">
                    <div className={`text-sm font-semibold transition-colors duration-200 ${
                      isHovered ? 'text-secondary-900' : 'text-secondary-700'
                    }`}>
                      {item.name}
                    </div>
                    <div className="text-xs text-secondary-500">
                      {item.value} tasks ({item.percentage}%)
                    </div>
                  </div>
                  <div className={`text-lg font-bold transition-all duration-200 ${
                    isHovered ? 'scale-110' : ''
                  }`} style={{ color: item.color }}>
                    {item.value}
                  </div>
                </div>
              )
            })}
          </div>
        </div>

        {/* Summary Stats */}
        <div className="grid grid-cols-2 gap-6 mt-8 pt-6 border-t border-secondary-200/50">
          <div className="text-center p-4 rounded-2xl bg-gradient-to-br from-success-50 to-success-100/50">
            <div className="text-2xl font-bold text-success-600">89%</div>
            <div className="text-sm text-success-700 font-medium">Completion Rate</div>
            <div className="text-xs text-success-600 mt-1">+5% from last month</div>
          </div>
          <div className="text-center p-4 rounded-2xl bg-gradient-to-br from-primary-50 to-primary-100/50">
            <div className="text-2xl font-bold text-primary-600">2.3</div>
            <div className="text-sm text-primary-700 font-medium">Avg. Days to Complete</div>
            <div className="text-xs text-primary-600 mt-1">-0.5 days improvement</div>
          </div>
        </div>
      </CardContent>
    </Card>
  )
}
