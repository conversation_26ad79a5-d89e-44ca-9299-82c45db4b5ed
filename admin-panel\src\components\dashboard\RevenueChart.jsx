import React from 'react'
import {
  <PERSON><PERSON>hart,
  Line,
  XAxis,
  YAxis,
  CartesianGrid,
  Tooltip,
  ResponsiveContainer,
} from 'recharts'
import { format } from 'date-fns'

export const RevenueChart = ({ data }) => {
  const formatTooltipValue = (value, name) => {
    if (name === 'revenue') {
      return [`₹${value.toLocaleString()}`, 'Revenue']
    }
    return [value, name]
  }

  const formatXAxisLabel = (tickItem) => {
    return format(new Date(tickItem), 'MMM dd')
  }

  return (
    <div className="h-80">
      <ResponsiveContainer width="100%" height="100%">
        <LineChart data={data} margin={{ top: 5, right: 30, left: 20, bottom: 5 }}>
          <CartesianGrid strokeDasharray="3 3" stroke="#f0f0f0" />
          <XAxis
            dataKey="date"
            tickFormatter={formatXAxisLabel}
            stroke="#6b7280"
            fontSize={12}
          />
          <YAxis
            tickFormatter={(value) => `₹${(value / 1000).toFixed(0)}k`}
            stroke="#6b7280"
            fontSize={12}
          />
          <Tooltip
            formatter={formatTooltipValue}
            labelFormatter={(label) => format(new Date(label), 'MMM dd, yyyy')}
            contentStyle={{
              backgroundColor: '#fff',
              border: '1px solid #e5e7eb',
              borderRadius: '8px',
              boxShadow: '0 4px 6px -1px rgba(0, 0, 0, 0.1)',
            }}
          />
          <Line
            type="monotone"
            dataKey="revenue"
            stroke="#3b82f6"
            strokeWidth={2}
            dot={{ fill: '#3b82f6', strokeWidth: 2, r: 4 }}
            activeDot={{ r: 6, stroke: '#3b82f6', strokeWidth: 2 }}
          />
        </LineChart>
      </ResponsiveContainer>
    </div>
  )
}
