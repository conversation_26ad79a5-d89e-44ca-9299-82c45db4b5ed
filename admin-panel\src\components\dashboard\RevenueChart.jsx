import React from 'react'
import { <PERSON>, CardHeader, CardTitle, CardDescription, CardContent } from '@components/ui/Card'
import { LoadingSpinner } from '@components/ui/LoadingSpinner'
import { clsx } from 'clsx'

export const RevenueChart = ({ data = [], loading = false, className = '' }) => {
  if (loading) {
    return (
      <Card className={clsx('h-80', className)} gradient>
        <CardHeader>
          <div className="h-6 bg-gray-200 rounded w-32 mb-2 animate-pulse"></div>
          <div className="h-4 bg-gray-200 rounded w-48 animate-pulse"></div>
        </CardHeader>
        <CardContent>
          <div className="flex items-center justify-center h-48">
            <LoadingSpinner size="lg" />
          </div>
        </CardContent>
      </Card>
    )
  }

  const maxRevenue = Math.max(...data.map(d => d.revenue))
  const maxTasks = Math.max(...data.map(d => d.tasks))

  return (
    <Card className={clsx('h-80', className)} hover gradient>
      <CardHeader>
        <CardTitle size="lg">Revenue Overview</CardTitle>
        <CardDescription>Monthly revenue and task completion trends</CardDescription>
      </CardHeader>
      <CardContent>
        <div className="h-48 flex items-end justify-between space-x-2 px-2">
          {data.map((item, index) => (
            <div key={item.month} className="flex-1 flex flex-col items-center group relative">
              {/* Revenue Bar */}
              <div className="relative w-full flex flex-col items-center mb-2">
                <div
                  className="w-full bg-gradient-to-t from-primary-500 to-primary-400 rounded-t-lg transition-all duration-500 hover:from-primary-600 hover:to-primary-500 group-hover:scale-105 shadow-lg"
                  style={{
                    height: `${(item.revenue / maxRevenue) * 120}px`,
                    minHeight: '8px'
                  }}
                />

                {/* Tasks indicator */}
                <div
                  className="w-3/4 bg-gradient-to-t from-accent-500 to-accent-400 rounded-t-sm mt-1 transition-all duration-500 hover:from-accent-600 hover:to-accent-500 group-hover:scale-105"
                  style={{
                    height: `${(item.tasks / maxTasks) * 40}px`,
                    minHeight: '4px'
                  }}
                />
              </div>

              {/* Month label */}
              <div className="text-xs font-medium text-gray-600 group-hover:text-gray-900 transition-colors">
                {item.month}
              </div>

              {/* Hover tooltip */}
              <div className="absolute bottom-full mb-2 left-1/2 transform -translate-x-1/2 bg-gray-900 text-white text-xs rounded-lg px-3 py-2 opacity-0 group-hover:opacity-100 transition-opacity duration-200 pointer-events-none whitespace-nowrap z-10">
                <div className="font-semibold">${item.revenue.toLocaleString()}</div>
                <div className="text-gray-300">{item.tasks} tasks</div>
                <div className="absolute top-full left-1/2 transform -translate-x-1/2 border-4 border-transparent border-t-gray-900"></div>
              </div>
            </div>
          ))}
        </div>

        {/* Legend */}
        <div className="flex items-center justify-center space-x-6 mt-4 pt-4 border-t border-gray-200/60">
          <div className="flex items-center space-x-2">
            <div className="w-3 h-3 bg-gradient-to-r from-primary-500 to-primary-400 rounded-full"></div>
            <span className="text-sm text-gray-600">Revenue</span>
          </div>
          <div className="flex items-center space-x-2">
            <div className="w-3 h-3 bg-gradient-to-r from-accent-500 to-accent-400 rounded-full"></div>
            <span className="text-sm text-gray-600">Tasks</span>
          </div>
        </div>
      </CardContent>
    </Card>
  )
}
