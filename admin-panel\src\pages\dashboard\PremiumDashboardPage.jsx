import React, { useState, useEffect } from 'react'
import { He<PERSON><PERSON> } from 'react-helmet-async'
import {
  UsersIcon,
  ClipboardDocumentListIcon,
  CurrencyDollarIcon,
  ChartBarIcon,
  TruckIcon,
  CheckCircleIcon,
  ClockIcon,
  ExclamationTriangleIcon,
} from '@heroicons/react/24/outline'
import { PremiumStatsCard } from '@components/dashboard/PremiumStatsCard'
import { PremiumRevenueChart } from '@components/dashboard/PremiumRevenueChart'
import { PremiumTasksChart } from '@components/dashboard/PremiumTasksChart'

const statsData = [
  {
    title: 'Total Users',
    value: '12,847',
    change: '+12.5%',
    changeType: 'positive',
    icon: UsersIcon,
    trend: [45, 52, 48, 61, 55, 67, 72],
    accentColor: 'primary',
  },
  {
    title: 'Active Tasks',
    value: '1,234',
    change: '+8.2%',
    changeType: 'positive',
    icon: ClipboardDocumentListIcon,
    trend: [23, 34, 28, 45, 38, 52, 48],
    accentColor: 'success',
  },
  {
    title: 'Monthly Revenue',
    value: '$91,247',
    change: '+15.3%',
    changeType: 'positive',
    icon: CurrencyDollarIcon,
    trend: [67, 72, 69, 78, 82, 85, 91],
    accentColor: 'warning',
  },
  {
    title: 'Completion Rate',
    value: '94.2%',
    change: '+2.1%',
    changeType: 'positive',
    icon: ChartBarIcon,
    trend: [89, 91, 88, 92, 90, 93, 94],
    accentColor: 'success',
  },
]

const quickStats = [
  {
    title: 'Pending Deliveries',
    value: '156',
    change: '-5.2%',
    changeType: 'positive',
    icon: TruckIcon,
    accentColor: 'primary',
  },
  {
    title: 'Completed Today',
    value: '89',
    change: '+12.8%',
    changeType: 'positive',
    icon: CheckCircleIcon,
    accentColor: 'success',
  },
  {
    title: 'Avg. Response Time',
    value: '2.3m',
    change: '-18.5%',
    changeType: 'positive',
    icon: ClockIcon,
    accentColor: 'warning',
  },
  {
    title: 'Issues Reported',
    value: '12',
    change: '+3.2%',
    changeType: 'negative',
    icon: ExclamationTriangleIcon,
    accentColor: 'danger',
  },
]

export const PremiumDashboardPage = () => {
  const [loading, setLoading] = useState(true)

  useEffect(() => {
    // Simulate loading
    const timer = setTimeout(() => {
      setLoading(false)
    }, 1500)

    return () => clearTimeout(timer)
  }, [])

  return (
    <div className="space-y-8 animate-fade-in-up">
      <Helmet>
        <title>Dashboard - WorkBoy Admin</title>
      </Helmet>

      {/* Header */}
      <div className="relative">
        <div className="absolute inset-0 bg-gradient-to-r from-primary-500/10 via-transparent to-accent-500/10 rounded-3xl blur-3xl"></div>
        <div className="relative bg-white/80 backdrop-blur-sm rounded-3xl border border-secondary-200/50 p-8 shadow-lg">
          <div className="flex items-center justify-between">
            <div>
              <h1 className="text-4xl font-bold bg-gradient-to-r from-secondary-900 via-secondary-800 to-secondary-700 bg-clip-text text-transparent">
                Welcome back, Admin! 👋
              </h1>
              <p className="text-lg text-secondary-600 mt-2 font-medium">
                Here's what's happening with your business today.
              </p>
            </div>
            <div className="hidden lg:block">
              <div className="flex items-center space-x-4">
                <div className="text-right">
                  <div className="text-sm text-secondary-500 font-medium">Today</div>
                  <div className="text-2xl font-bold text-secondary-900">
                    {new Date().toLocaleDateString('en-US', { 
                      weekday: 'long',
                      month: 'short',
                      day: 'numeric'
                    })}
                  </div>
                </div>
                <div className="h-16 w-16 rounded-2xl bg-gradient-to-br from-primary-500 to-primary-600 flex items-center justify-center shadow-lg shadow-primary-500/25">
                  <ChartBarIcon className="h-8 w-8 text-white" />
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Main Stats Grid */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        {statsData.map((stat, index) => (
          <div
            key={stat.title}
            className="animate-fade-in-up"
            style={{ animationDelay: `${index * 100}ms` }}
          >
            <PremiumStatsCard
              title={stat.title}
              value={stat.value}
              change={stat.change}
              changeType={stat.changeType}
              icon={stat.icon}
              trend={stat.trend}
              loading={loading}
              accentColor={stat.accentColor}
            />
          </div>
        ))}
      </div>

      {/* Charts Section */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
        <div className="animate-fade-in-left">
          <PremiumRevenueChart loading={loading} />
        </div>
        <div className="animate-fade-in-right">
          <PremiumTasksChart loading={loading} />
        </div>
      </div>

      {/* Quick Stats */}
      <div className="space-y-6">
        <div className="flex items-center justify-between">
          <h2 className="text-2xl font-bold text-secondary-900">Quick Overview</h2>
          <button className="px-4 py-2 text-sm font-semibold text-primary-600 hover:text-primary-700 hover:bg-primary-50 rounded-xl transition-all duration-200">
            View All →
          </button>
        </div>
        
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
          {quickStats.map((stat, index) => (
            <div
              key={stat.title}
              className="animate-fade-in-up"
              style={{ animationDelay: `${(index + 4) * 100}ms` }}
            >
              <PremiumStatsCard
                title={stat.title}
                value={stat.value}
                change={stat.change}
                changeType={stat.changeType}
                icon={stat.icon}
                loading={loading}
                accentColor={stat.accentColor}
              />
            </div>
          ))}
        </div>
      </div>

      {/* Recent Activity & Notifications */}
      <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
        {/* Recent Activity */}
        <div className="lg:col-span-2 animate-fade-in-up" style={{ animationDelay: '800ms' }}>
          <div className="bg-white/80 backdrop-blur-sm rounded-3xl border border-secondary-200/50 p-6 shadow-lg hover:shadow-xl transition-all duration-300">
            <div className="flex items-center justify-between mb-6">
              <h3 className="text-xl font-bold text-secondary-900">Recent Activity</h3>
              <button className="text-sm font-semibold text-primary-600 hover:text-primary-700">
                View All
              </button>
            </div>
            
            <div className="space-y-4">
              {[
                { action: 'New user registered', user: 'John Doe', time: '2 minutes ago', type: 'user' },
                { action: 'Task completed', user: 'WorkBoy #1234', time: '5 minutes ago', type: 'task' },
                { action: 'Payment received', user: 'Customer #5678', time: '12 minutes ago', type: 'payment' },
                { action: 'New task created', user: 'Admin', time: '18 minutes ago', type: 'task' },
              ].map((activity, index) => (
                <div key={index} className="flex items-center space-x-4 p-3 rounded-xl hover:bg-secondary-50 transition-colors duration-200">
                  <div className={`w-10 h-10 rounded-xl flex items-center justify-center ${
                    activity.type === 'user' ? 'bg-primary-100 text-primary-600' :
                    activity.type === 'task' ? 'bg-success-100 text-success-600' :
                    'bg-warning-100 text-warning-600'
                  }`}>
                    {activity.type === 'user' ? <UsersIcon className="h-5 w-5" /> :
                     activity.type === 'task' ? <ClipboardDocumentListIcon className="h-5 w-5" /> :
                     <CurrencyDollarIcon className="h-5 w-5" />}
                  </div>
                  <div className="flex-1">
                    <div className="text-sm font-semibold text-secondary-900">{activity.action}</div>
                    <div className="text-xs text-secondary-500">{activity.user} • {activity.time}</div>
                  </div>
                </div>
              ))}
            </div>
          </div>
        </div>

        {/* Quick Actions */}
        <div className="animate-fade-in-up" style={{ animationDelay: '900ms' }}>
          <div className="bg-white/80 backdrop-blur-sm rounded-3xl border border-secondary-200/50 p-6 shadow-lg hover:shadow-xl transition-all duration-300">
            <h3 className="text-xl font-bold text-secondary-900 mb-6">Quick Actions</h3>
            
            <div className="space-y-3">
              {[
                { title: 'Add New User', icon: UsersIcon, color: 'primary' },
                { title: 'Create Task', icon: ClipboardDocumentListIcon, color: 'success' },
                { title: 'View Reports', icon: ChartBarIcon, color: 'warning' },
                { title: 'Manage Settings', icon: CurrencyDollarIcon, color: 'secondary' },
              ].map((action, index) => (
                <button
                  key={action.title}
                  className="w-full flex items-center space-x-3 p-3 rounded-xl text-left hover:bg-secondary-50 transition-all duration-200 hover:scale-[1.02] group"
                >
                  <div className={`w-10 h-10 rounded-xl flex items-center justify-center bg-${action.color}-100 text-${action.color}-600 group-hover:scale-110 transition-transform duration-200`}>
                    <action.icon className="h-5 w-5" />
                  </div>
                  <span className="text-sm font-semibold text-secondary-900">{action.title}</span>
                </button>
              ))}
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}
