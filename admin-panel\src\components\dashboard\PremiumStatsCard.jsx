import React from 'react'
import { ArrowUpIcon, ArrowDownIcon } from '@heroicons/react/24/outline'
import { clsx } from 'clsx'

export const PremiumStatsCard = ({ 
  title, 
  value, 
  change, 
  changeType, 
  icon: Icon, 
  trend = [], 
  loading = false,
  gradient = 'from-primary-500 to-primary-600',
  accentColor = 'primary'
}) => {
  const isPositive = changeType === 'positive'
  const isNegative = changeType === 'negative'

  const colorClasses = {
    primary: {
      bg: 'from-primary-500 to-primary-600',
      text: 'text-primary-600',
      light: 'bg-primary-50',
      border: 'border-primary-200',
      glow: 'shadow-primary-500/20'
    },
    success: {
      bg: 'from-success-500 to-success-600',
      text: 'text-success-600',
      light: 'bg-success-50',
      border: 'border-success-200',
      glow: 'shadow-success-500/20'
    },
    warning: {
      bg: 'from-warning-500 to-warning-600',
      text: 'text-warning-600',
      light: 'bg-warning-50',
      border: 'border-warning-200',
      glow: 'shadow-warning-500/20'
    },
    danger: {
      bg: 'from-danger-500 to-danger-600',
      text: 'text-danger-600',
      light: 'bg-danger-50',
      border: 'border-danger-200',
      glow: 'shadow-danger-500/20'
    }
  }

  const colors = colorClasses[accentColor] || colorClasses.primary

  if (loading) {
    return (
      <div className="relative overflow-hidden rounded-3xl bg-white/80 backdrop-blur-sm border border-secondary-200/50 p-6 shadow-lg hover:shadow-xl transition-all duration-300">
        <div className="animate-pulse">
          <div className="flex items-center justify-between">
            <div className="space-y-3">
              <div className="h-4 bg-secondary-200 rounded-lg w-24"></div>
              <div className="h-8 bg-secondary-200 rounded-lg w-32"></div>
              <div className="h-4 bg-secondary-200 rounded-lg w-20"></div>
            </div>
            <div className="h-12 w-12 bg-secondary-200 rounded-2xl"></div>
          </div>
          <div className="mt-6 flex space-x-1">
            {[...Array(8)].map((_, i) => (
              <div key={i} className="h-8 w-2 bg-secondary-200 rounded-sm"></div>
            ))}
          </div>
        </div>
      </div>
    )
  }

  return (
    <div className="group relative overflow-hidden rounded-3xl bg-white/80 backdrop-blur-sm border border-secondary-200/50 p-6 shadow-lg hover:shadow-xl hover:shadow-primary-500/10 transition-all duration-300 hover:scale-[1.02] hover:-translate-y-1">
      {/* Background Pattern */}
      <div className="absolute inset-0 bg-gradient-to-br from-white via-white to-secondary-50/30 opacity-60"></div>
      
      {/* Floating Orbs */}
      <div className="absolute -top-4 -right-4 h-24 w-24 bg-gradient-to-br from-primary-400/20 to-primary-600/20 rounded-full blur-xl group-hover:scale-125 transition-transform duration-500"></div>
      <div className="absolute -bottom-4 -left-4 h-16 w-16 bg-gradient-to-br from-accent-400/20 to-accent-600/20 rounded-full blur-lg group-hover:scale-125 transition-transform duration-500"></div>
      
      <div className="relative">
        {/* Header */}
        <div className="flex items-center justify-between mb-4">
          <div className="space-y-1">
            <p className="text-sm font-medium text-secondary-600 tracking-wide">{title}</p>
            <div className="flex items-baseline space-x-2">
              <h3 className="text-3xl font-bold text-secondary-900 tracking-tight">{value}</h3>
              {change && (
                <div className={clsx(
                  'flex items-center space-x-1 px-2.5 py-1 rounded-full text-xs font-semibold',
                  isPositive && 'bg-success-100 text-success-700',
                  isNegative && 'bg-danger-100 text-danger-700',
                  !isPositive && !isNegative && 'bg-secondary-100 text-secondary-700'
                )}>
                  {isPositive && <ArrowUpIcon className="h-3 w-3" />}
                  {isNegative && <ArrowDownIcon className="h-3 w-3" />}
                  <span>{change}</span>
                </div>
              )}
            </div>
          </div>
          
          {/* Icon */}
          <div className={clsx(
            'relative p-3 rounded-2xl bg-gradient-to-br shadow-lg transition-all duration-300 group-hover:scale-110 group-hover:rotate-3',
            colors.bg,
            colors.glow
          )}>
            <Icon className="h-7 w-7 text-white" />
            <div className="absolute inset-0 rounded-2xl bg-white/20 opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>
          </div>
        </div>

        {/* Mini Trend Chart */}
        {trend.length > 0 && (
          <div className="mt-6">
            <div className="flex items-end justify-between h-12 space-x-1">
              {trend.map((point, index) => {
                const height = Math.max((point / Math.max(...trend)) * 100, 10)
                return (
                  <div
                    key={index}
                    className={clsx(
                      'rounded-t-sm transition-all duration-500 group-hover:opacity-80',
                      `bg-gradient-to-t ${colors.bg}`
                    )}
                    style={{
                      height: `${height}%`,
                      width: `${100 / trend.length - 2}%`,
                      animationDelay: `${index * 100}ms`
                    }}
                  />
                )
              })}
            </div>
            <div className="mt-2 flex justify-between text-xs text-secondary-500">
              <span>7 days ago</span>
              <span>Today</span>
            </div>
          </div>
        )}

        {/* Progress Bar (if no trend) */}
        {trend.length === 0 && change && (
          <div className="mt-6">
            <div className="flex items-center justify-between text-xs text-secondary-500 mb-2">
              <span>Progress</span>
              <span>{change}</span>
            </div>
            <div className="h-2 bg-secondary-100 rounded-full overflow-hidden">
              <div 
                className={clsx(
                  'h-full rounded-full bg-gradient-to-r transition-all duration-1000',
                  colors.bg
                )}
                style={{ 
                  width: `${Math.min(Math.abs(parseFloat(change)) * 2, 100)}%`,
                  animationDelay: '300ms'
                }}
              />
            </div>
          </div>
        )}
      </div>

      {/* Hover Glow Effect */}
      <div className="absolute inset-0 rounded-3xl bg-gradient-to-br from-primary-500/5 to-accent-500/5 opacity-0 group-hover:opacity-100 transition-opacity duration-300 pointer-events-none"></div>
    </div>
  )
}
