import React from 'react'
import { StarIcon } from '@heroicons/react/24/solid'
import { StarIcon as StarOutlineIcon } from '@heroicons/react/24/outline'
import { <PERSON>, CardHeader, CardTitle, CardContent } from '@components/ui/Card'
import { clsx } from 'clsx'

// Mock data - replace with real data from API
const mockWorkBoys = [
  {
    id: 1,
    name: '<PERSON>',
    avatar: null,
    rating: 4.9,
    completedTasks: 156,
    earnings: 12450,
    category: 'Cleaning',
    status: 'active'
  },
  {
    id: 2,
    name: '<PERSON>',
    avatar: null,
    rating: 4.8,
    completedTasks: 142,
    earnings: 11200,
    category: 'Plumbing',
    status: 'active'
  },
  {
    id: 3,
    name: '<PERSON>',
    avatar: null,
    rating: 4.7,
    completedTasks: 128,
    earnings: 9800,
    category: 'Electrical',
    status: 'active'
  },
  {
    id: 4,
    name: '<PERSON>',
    avatar: null,
    rating: 4.6,
    completedTasks: 115,
    earnings: 8900,
    category: 'Gardening',
    status: 'active'
  },
  {
    id: 5,
    name: '<PERSON>',
    avatar: null,
    rating: 4.5,
    completedTasks: 98,
    earnings: 7650,
    category: 'Carpentry',
    status: 'active'
  }
]

const StarRating = ({ rating, className = '' }) => {
  const fullStars = Math.floor(rating)
  const hasHalfStar = rating % 1 !== 0
  const emptyStars = 5 - fullStars - (hasHalfStar ? 1 : 0)

  return (
    <div className={clsx('flex items-center', className)}>
      {/* Full stars */}
      {[...Array(fullStars)].map((_, i) => (
        <StarIcon key={`full-${i}`} className="h-4 w-4 text-yellow-400" />
      ))}
      
      {/* Half star */}
      {hasHalfStar && (
        <div className="relative">
          <StarOutlineIcon className="h-4 w-4 text-gray-300" />
          <div className="absolute inset-0 overflow-hidden" style={{ width: '50%' }}>
            <StarIcon className="h-4 w-4 text-yellow-400" />
          </div>
        </div>
      )}
      
      {/* Empty stars */}
      {[...Array(emptyStars)].map((_, i) => (
        <StarOutlineIcon key={`empty-${i}`} className="h-4 w-4 text-gray-300" />
      ))}
      
      <span className="ml-1 text-sm text-gray-600">
        {rating.toFixed(1)}
      </span>
    </div>
  )
}

export const TopWorkBoys = ({ workboys = mockWorkBoys, loading = false, className = '' }) => {
  if (loading) {
    return (
      <Card className={className} gradient>
        <CardHeader>
          <div className="h-6 bg-gray-200 rounded w-40 animate-pulse"></div>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            {[...Array(5)].map((_, i) => (
              <div key={i} className="flex items-center space-x-4 p-4 animate-pulse">
                <div className="w-12 h-12 bg-gray-200 rounded-full"></div>
                <div className="flex-1 space-y-2">
                  <div className="h-4 bg-gray-200 rounded w-3/4"></div>
                  <div className="h-3 bg-gray-200 rounded w-1/2"></div>
                </div>
                <div className="space-y-2">
                  <div className="h-4 bg-gray-200 rounded w-16"></div>
                  <div className="h-3 bg-gray-200 rounded w-12"></div>
                </div>
              </div>
            ))}
          </div>
        </CardContent>
      </Card>
    )
  }

  return (
    <Card className={className} hover gradient>
      <CardHeader>
        <CardTitle size="lg">Top WorkBoys</CardTitle>
        <CardDescription>Highest performing service providers</CardDescription>
      </CardHeader>
      <CardContent>
        <div className="space-y-3">
          {workboys.map((workboy, index) => (
            <div
              key={workboy.id}
              className="group relative flex items-center justify-between p-4 bg-gradient-to-r from-gray-50 to-gray-50/50 rounded-xl hover:from-primary-50 hover:to-primary-50/50 transition-all duration-300 cursor-pointer border border-gray-200/60 hover:border-primary-200"
            >
              <div className="flex items-center space-x-4">
                <div className="flex-shrink-0">
                  <div className="relative">
                    {workboy.avatar ? (
                      <img
                        className="h-12 w-12 rounded-xl object-cover shadow-md group-hover:scale-105 transition-transform duration-200"
                        src={workboy.avatar}
                        alt={workboy.name}
                      />
                    ) : (
                      <div className="h-12 w-12 rounded-xl bg-gradient-to-br from-primary-500 to-primary-700 flex items-center justify-center shadow-md group-hover:scale-105 transition-transform duration-200">
                        <span className="text-sm font-bold text-white">
                          {workboy.name.split(' ').map(n => n[0]).join('')}
                        </span>
                      </div>
                    )}
                    <div className="absolute -top-2 -left-2 flex items-center justify-center h-6 w-6 bg-gradient-to-r from-yellow-400 to-yellow-500 rounded-full text-xs font-bold text-white shadow-lg">
                      {index + 1}
                    </div>
                  </div>
                </div>

                <div className="min-w-0 flex-1">
                  <p className="text-sm font-semibold text-gray-900 truncate group-hover:text-primary-700 transition-colors">
                    {workboy.name}
                  </p>
                  <p className="text-xs text-gray-600 font-medium">
                    {workboy.category}
                  </p>
                  <StarRating rating={workboy.rating} className="mt-1" />
                </div>
              </div>

              <div className="text-right">
                <p className="text-sm font-bold text-gray-900 group-hover:text-primary-700 transition-colors">
                  ${workboy.earnings.toLocaleString()}
                </p>
                <p className="text-xs text-gray-500">
                  {workboy.completedTasks} tasks
                </p>
              </div>

              {/* Hover effect overlay */}
              <div className="absolute inset-0 bg-gradient-to-r from-primary-500/5 to-primary-600/5 rounded-xl opacity-0 group-hover:opacity-100 transition-opacity duration-300 pointer-events-none" />
            </div>
          ))}
        </div>

        <div className="mt-6 pt-4 border-t border-gray-200/60">
          <a
            href="/workboys"
            className="inline-flex items-center text-sm font-semibold text-primary-600 hover:text-primary-700 transition-colors group"
          >
            View all WorkBoys
            <span className="ml-1 group-hover:translate-x-1 transition-transform duration-200" aria-hidden="true">→</span>
          </a>
        </div>
      </CardContent>
    </Card>
  )
}

export default TopWorkBoys
