import React from 'react'
import { StarIcon } from '@heroicons/react/24/solid'
import { StarIcon as StarOutlineIcon } from '@heroicons/react/24/outline'
import { <PERSON>, CardHeader, CardTitle, CardContent } from '@components/ui/Card'
import { clsx } from 'clsx'

// Mock data - replace with real data from API
const mockWorkBoys = [
  {
    id: 1,
    name: '<PERSON>',
    avatar: null,
    rating: 4.9,
    completedTasks: 156,
    earnings: 12450,
    category: 'Cleaning',
    status: 'active'
  },
  {
    id: 2,
    name: '<PERSON>',
    avatar: null,
    rating: 4.8,
    completedTasks: 142,
    earnings: 11200,
    category: 'Plumbing',
    status: 'active'
  },
  {
    id: 3,
    name: '<PERSON>',
    avatar: null,
    rating: 4.7,
    completedTasks: 128,
    earnings: 9800,
    category: 'Electrical',
    status: 'active'
  },
  {
    id: 4,
    name: '<PERSON>',
    avatar: null,
    rating: 4.6,
    completedTasks: 115,
    earnings: 8900,
    category: 'Gardening',
    status: 'active'
  },
  {
    id: 5,
    name: '<PERSON>',
    avatar: null,
    rating: 4.5,
    completedTasks: 98,
    earnings: 7650,
    category: 'Carpentry',
    status: 'active'
  }
]

const StarRating = ({ rating, className = '' }) => {
  const fullStars = Math.floor(rating)
  const hasHalfStar = rating % 1 !== 0
  const emptyStars = 5 - fullStars - (hasHalfStar ? 1 : 0)

  return (
    <div className={clsx('flex items-center', className)}>
      {/* Full stars */}
      {[...Array(fullStars)].map((_, i) => (
        <StarIcon key={`full-${i}`} className="h-4 w-4 text-yellow-400" />
      ))}
      
      {/* Half star */}
      {hasHalfStar && (
        <div className="relative">
          <StarOutlineIcon className="h-4 w-4 text-gray-300" />
          <div className="absolute inset-0 overflow-hidden" style={{ width: '50%' }}>
            <StarIcon className="h-4 w-4 text-yellow-400" />
          </div>
        </div>
      )}
      
      {/* Empty stars */}
      {[...Array(emptyStars)].map((_, i) => (
        <StarOutlineIcon key={`empty-${i}`} className="h-4 w-4 text-gray-300" />
      ))}
      
      <span className="ml-1 text-sm text-gray-600">
        {rating.toFixed(1)}
      </span>
    </div>
  )
}

export const TopWorkBoys = ({ workboys = mockWorkBoys, className = '' }) => {
  return (
    <Card className={className}>
      <CardHeader>
        <CardTitle>Top Performing WorkBoys</CardTitle>
      </CardHeader>
      <CardContent>
        <div className="space-y-4">
          {workboys.map((workboy, index) => (
            <div
              key={workboy.id}
              className="flex items-center justify-between p-4 bg-gray-50 rounded-lg hover:bg-gray-100 transition-colors"
            >
              <div className="flex items-center space-x-4">
                <div className="flex-shrink-0">
                  <div className="relative">
                    {workboy.avatar ? (
                      <img
                        className="h-10 w-10 rounded-full"
                        src={workboy.avatar}
                        alt={workboy.name}
                      />
                    ) : (
                      <div className="h-10 w-10 rounded-full bg-primary-600 flex items-center justify-center">
                        <span className="text-sm font-medium text-white">
                          {workboy.name.split(' ').map(n => n[0]).join('')}
                        </span>
                      </div>
                    )}
                    <div className="absolute -top-1 -left-1 flex items-center justify-center h-6 w-6 bg-yellow-400 rounded-full text-xs font-bold text-white">
                      {index + 1}
                    </div>
                  </div>
                </div>
                
                <div className="min-w-0 flex-1">
                  <p className="text-sm font-medium text-gray-900 truncate">
                    {workboy.name}
                  </p>
                  <p className="text-sm text-gray-500">
                    {workboy.category}
                  </p>
                  <StarRating rating={workboy.rating} className="mt-1" />
                </div>
              </div>
              
              <div className="text-right">
                <p className="text-sm font-medium text-gray-900">
                  ${workboy.earnings.toLocaleString()}
                </p>
                <p className="text-sm text-gray-500">
                  {workboy.completedTasks} tasks
                </p>
              </div>
            </div>
          ))}
        </div>
        
        <div className="mt-6">
          <a
            href="/workboys"
            className="text-sm font-medium text-primary-600 hover:text-primary-500"
          >
            View all WorkBoys
            <span aria-hidden="true"> &rarr;</span>
          </a>
        </div>
      </CardContent>
    </Card>
  )
}

export default TopWorkBoys
