import React from 'react'
import { formatDistanceToNow } from 'date-fns'
import { 
  UserIcon, 
  ClipboardDocumentListIcon, 
  CurrencyDollarIcon,
  CheckCircleIcon,
  XCircleIcon
} from '@heroicons/react/24/outline'
import { Card, CardHeader, CardTitle, CardDescription, CardContent } from '@components/ui/Card'
import { clsx } from 'clsx'

const activityIcons = {
  user: UserIcon,
  task: ClipboardDocumentListIcon,
  payment: CurrencyDollarIcon,
  success: CheckCircleIcon,
  error: XCircleIcon
}

const activityColors = {
  user: 'text-blue-600 bg-blue-100',
  task: 'text-green-600 bg-green-100',
  payment: 'text-yellow-600 bg-yellow-100',
  success: 'text-green-600 bg-green-100',
  error: 'text-red-600 bg-red-100'
}

// Mock data - replace with real data from API
const mockActivities = [
  {
    id: 1,
    type: 'user',
    title: 'New user registered',
    description: '<PERSON> joined as a customer',
    timestamp: new Date(Date.now() - 1000 * 60 * 15), // 15 minutes ago
    user: '<PERSON>'
  },
  {
    id: 2,
    type: 'task',
    title: 'Task completed',
    description: 'House cleaning task completed by <PERSON>',
    timestamp: new Date(Date.now() - 1000 * 60 * 30), // 30 minutes ago
    user: 'Mike Wilson'
  },
  {
    id: 3,
    type: 'payment',
    title: 'Payment processed',
    description: 'Payment of $150 processed successfully',
    timestamp: new Date(Date.now() - 1000 * 60 * 45), // 45 minutes ago
    amount: 150
  },
  {
    id: 4,
    type: 'user',
    title: 'WorkBoy verified',
    description: 'Sarah Johnson KYC verification approved',
    timestamp: new Date(Date.now() - 1000 * 60 * 60), // 1 hour ago
    user: 'Sarah Johnson'
  },
  {
    id: 5,
    type: 'task',
    title: 'New task created',
    description: 'Plumbing repair task posted by Alex Brown',
    timestamp: new Date(Date.now() - 1000 * 60 * 90), // 1.5 hours ago
    user: 'Alex Brown'
  }
]

export const RecentActivity = ({ activities = mockActivities, loading = false, className = '' }) => {
  if (loading) {
    return (
      <Card className={className} gradient>
        <CardHeader>
          <div className="h-6 bg-gray-200 rounded w-32 animate-pulse"></div>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            {[...Array(5)].map((_, i) => (
              <div key={i} className="flex items-center space-x-3 animate-pulse">
                <div className="w-8 h-8 bg-gray-200 rounded-full"></div>
                <div className="flex-1 space-y-2">
                  <div className="h-4 bg-gray-200 rounded w-3/4"></div>
                  <div className="h-3 bg-gray-200 rounded w-1/2"></div>
                </div>
              </div>
            ))}
          </div>
        </CardContent>
      </Card>
    )
  }

  return (
    <Card className={className} hover gradient>
      <CardHeader>
        <CardTitle size="lg">Recent Activity</CardTitle>
        <CardDescription>Latest platform activities and updates</CardDescription>
      </CardHeader>
      <CardContent>
        <div className="flow-root">
          <ul className="-mb-8">
            {activities.map((activity, activityIdx) => {
              const Icon = activityIcons[activity.type]
              return (
                <li key={activity.id} className="group">
                  <div className="relative pb-8">
                    {activityIdx !== activities.length - 1 ? (
                      <span
                        className="absolute left-4 top-4 -ml-px h-full w-0.5 bg-gradient-to-b from-gray-200 to-gray-100"
                        aria-hidden="true"
                      />
                    ) : null}
                    <div className="relative flex space-x-4">
                      <div>
                        <span
                          className={clsx(
                            activityColors[activity.type],
                            'h-8 w-8 rounded-full flex items-center justify-center ring-4 ring-white shadow-md group-hover:scale-110 transition-transform duration-200'
                          )}
                        >
                          <Icon className="h-4 w-4" aria-hidden="true" />
                        </span>
                      </div>
                      <div className="flex min-w-0 flex-1 justify-between space-x-4 pt-1">
                        <div className="min-w-0 flex-1">
                          <p className="text-sm font-semibold text-gray-900 group-hover:text-primary-600 transition-colors">
                            {activity.title}
                          </p>
                          <p className="text-sm text-gray-600 mt-1">
                            {activity.description}
                          </p>
                        </div>
                        <div className="whitespace-nowrap text-right">
                          <time
                            dateTime={activity.timestamp.toISOString()}
                            className="text-xs text-gray-500 font-medium"
                          >
                            {formatDistanceToNow(activity.timestamp, { addSuffix: true })}
                          </time>
                        </div>
                      </div>
                    </div>
                  </div>
                </li>
              )
            })}
          </ul>
        </div>
        <div className="mt-6 pt-4 border-t border-gray-200/60">
          <a
            href="/activity"
            className="inline-flex items-center text-sm font-semibold text-primary-600 hover:text-primary-700 transition-colors group"
          >
            View all activity
            <span className="ml-1 group-hover:translate-x-1 transition-transform duration-200" aria-hidden="true">→</span>
          </a>
        </div>
      </CardContent>
    </Card>
  )
}

export default RecentActivity
