import React, { Fragment } from 'react'
import { Link, useLocation } from 'react-router-dom'
import { Dialog, Transition } from '@headlessui/react'
import { XMarkIcon } from '@heroicons/react/24/outline'
import {
  HomeIcon,
  UsersIcon,
  ClipboardDocumentListIcon,
  CreditCardIcon,
  ChartBarIcon,
  CogIcon,
  UserGroupIcon,
  DocumentCheckIcon,
  TagIcon,
  BanknotesIcon,
  SparklesIcon,
  BoltIcon,
  FireIcon,
  ShieldCheckIcon,
} from '@heroicons/react/24/outline'
import { useAuth } from '@context/AuthContext'
import { clsx } from 'clsx'

const navigation = [
  {
    name: 'Dashboard',
    href: '/dashboard',
    icon: HomeIcon,
    badge: null,
    gradient: 'from-primary-500 to-primary-600'
  },
  {
    name: 'User Management',
    icon: UsersIcon,
    gradient: 'from-blue-500 to-blue-600',
    children: [
      { name: 'Customers', href: '/users', icon: UsersIcon },
      { name: 'Work-Boys', href: '/workboys', icon: UserGroupIcon },
      { name: 'KYC Verification', href: '/kyc-verification', icon: ShieldCheckIcon, badge: '3' },
    ],
  },
  {
    name: 'Task Management',
    icon: ClipboardDocumentListIcon,
    gradient: 'from-emerald-500 to-emerald-600',
    children: [
      { name: 'All Tasks', href: '/tasks', icon: ClipboardDocumentListIcon },
      { name: 'Categories', href: '/categories', icon: TagIcon },
    ],
  },
  {
    name: 'Financial',
    icon: CreditCardIcon,
    gradient: 'from-amber-500 to-amber-600',
    children: [
      { name: 'Payments', href: '/payments', icon: CreditCardIcon },
      { name: 'Transactions', href: '/transactions', icon: BanknotesIcon },
      { name: 'Reports', href: '/reports', icon: ChartBarIcon },
    ],
  },
  {
    name: 'Analytics',
    href: '/analytics',
    icon: ChartBarIcon,
    gradient: 'from-purple-500 to-purple-600',
    badge: 'Pro'
  },
  {
    name: 'Settings',
    icon: CogIcon,
    gradient: 'from-secondary-500 to-secondary-600',
    children: [
      { name: 'General Settings', href: '/settings', icon: CogIcon },
      { name: 'Admin Users', href: '/admin-users', icon: ShieldCheckIcon },
    ],
  },
]

export const Sidebar = ({ open, setOpen }) => {
  const location = useLocation()
  const { user } = useAuth()

  const SidebarContent = () => (
    <div className="flex h-full flex-col">
      {/* Logo */}
      <div className="flex h-16 shrink-0 items-center px-6">
        <div className="flex items-center">
          <div className="flex h-8 w-8 items-center justify-center rounded-lg bg-primary-600">
            <span className="text-sm font-bold text-white">W</span>
          </div>
          <span className="ml-2 text-lg font-semibold text-gray-900">
            WorkBoy Admin
          </span>
        </div>
      </div>

      {/* Navigation */}
      <nav className="flex-1 space-y-1 px-3 py-4">
        {navigation.map((item) => (
          <NavigationItem
            key={item.name}
            item={item}
            currentPath={location.pathname}
          />
        ))}
      </nav>

      {/* User info */}
      <div className="border-t border-gray-200 p-4">
        <div className="flex items-center">
          <div className="flex h-8 w-8 items-center justify-center rounded-full bg-primary-100">
            <span className="text-sm font-medium text-primary-600">
              {user?.name?.charAt(0) || 'A'}
            </span>
          </div>
          <div className="ml-3">
            <p className="text-sm font-medium text-gray-700">{user?.name}</p>
            <p className="text-xs text-gray-500 capitalize">{user?.role}</p>
          </div>
        </div>
      </div>
    </div>
  )

  return (
    <>
      {/* Mobile sidebar */}
      <Transition.Root show={open} as={Fragment}>
        <Dialog as="div" className="relative z-50 lg:hidden" onClose={setOpen}>
          <Transition.Child
            as={Fragment}
            enter="transition-opacity ease-linear duration-300"
            enterFrom="opacity-0"
            enterTo="opacity-100"
            leave="transition-opacity ease-linear duration-300"
            leaveFrom="opacity-100"
            leaveTo="opacity-0"
          >
            <div className="fixed inset-0 bg-gray-900/80" />
          </Transition.Child>

          <div className="fixed inset-0 flex">
            <Transition.Child
              as={Fragment}
              enter="transition ease-in-out duration-300 transform"
              enterFrom="-translate-x-full"
              enterTo="translate-x-0"
              leave="transition ease-in-out duration-300 transform"
              leaveFrom="translate-x-0"
              leaveTo="-translate-x-full"
            >
              <Dialog.Panel className="relative mr-16 flex w-full max-w-xs flex-1">
                <Transition.Child
                  as={Fragment}
                  enter="ease-in-out duration-300"
                  enterFrom="opacity-0"
                  enterTo="opacity-100"
                  leave="ease-in-out duration-300"
                  leaveFrom="opacity-100"
                  leaveTo="opacity-0"
                >
                  <div className="absolute left-full top-0 flex w-16 justify-center pt-5">
                    <button
                      type="button"
                      className="-m-2.5 p-2.5"
                      onClick={() => setOpen(false)}
                    >
                      <span className="sr-only">Close sidebar</span>
                      <XMarkIcon className="h-6 w-6 text-white" aria-hidden="true" />
                    </button>
                  </div>
                </Transition.Child>
                <div className="flex grow flex-col gap-y-5 overflow-y-auto bg-white">
                  <SidebarContent />
                </div>
              </Dialog.Panel>
            </Transition.Child>
          </div>
        </Dialog>
      </Transition.Root>

      {/* Desktop sidebar */}
      <div className="hidden lg:fixed lg:inset-y-0 lg:z-50 lg:flex lg:w-72 lg:flex-col">
        <div className="flex grow flex-col gap-y-5 overflow-y-auto border-r border-gray-200 bg-white">
          <SidebarContent />
        </div>
      </div>
    </>
  )
}

const NavigationItem = ({ item, currentPath }) => {
  const [expanded, setExpanded] = React.useState(
    item.children?.some(child => currentPath.startsWith(child.href)) || false
  )

  if (item.children) {
    return (
      <div>
        <button
          onClick={() => setExpanded(!expanded)}
          className={clsx(
            'sidebar-link w-full justify-between',
            item.children.some(child => currentPath.startsWith(child.href)) && 'sidebar-link-active'
          )}
        >
          <div className="flex items-center">
            <item.icon className="mr-3 h-5 w-5" />
            {item.name}
          </div>
          <svg
            className={clsx(
              'h-4 w-4 transition-transform',
              expanded && 'rotate-90'
            )}
            fill="none"
            viewBox="0 0 24 24"
            stroke="currentColor"
          >
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5l7 7-7 7" />
          </svg>
        </button>
        {expanded && (
          <div className="ml-6 mt-1 space-y-1">
            {item.children.map((child) => (
              <Link
                key={child.name}
                to={child.href}
                className={clsx(
                  'sidebar-link',
                  currentPath.startsWith(child.href) && 'sidebar-link-active'
                )}
              >
                {child.name}
              </Link>
            ))}
          </div>
        )}
      </div>
    )
  }

  return (
    <Link
      to={item.href}
      className={clsx(
        'sidebar-link',
        currentPath === item.href && 'sidebar-link-active'
      )}
    >
      <item.icon className="mr-3 h-5 w-5" />
      {item.name}
    </Link>
  )
}
