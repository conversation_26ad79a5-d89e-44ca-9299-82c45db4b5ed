# WorkBoy Platform API Integration Guide

## Overview

This guide provides comprehensive information about integrating with the WorkBoy platform APIs across all applications (Customer Web, Customer Mobile, Work-Boy Mobile, and Admin Panel).

## Base URLs

- **Development**: `http://localhost:8080/api/v1`
- **Production**: `https://api.workboy.com/api/v1`
- **WebSocket**: `ws://localhost:8080` (Development) / `wss://ws.workboy.com` (Production)

## Authentication

All API endpoints (except public ones) require authentication using JWT tokens.

### Headers Required
```
Authorization: Bearer <jwt_token>
Content-Type: application/json
Accept: application/json
```

### Token Management
- Tokens expire after 24 hours
- Refresh tokens are valid for 30 days
- Store tokens securely (HttpOnly cookies for web, SecureStore for mobile)

## API Endpoints by Platform

### Customer Web Application

#### Authentication
- `POST /auth/register` - Customer registration
- `POST /auth/login` - Customer login
- `POST /auth/logout` - Logout
- `POST /auth/refresh` - Refresh token
- `GET /auth/profile` - Get user profile
- `PUT /auth/profile` - Update profile

#### Address Management
- `GET /customer/addresses` - List addresses
- `POST /customer/addresses` - Create address
- `PUT /customer/addresses/{id}` - Update address
- `DELETE /customer/addresses/{id}` - Delete address

#### Task Management
- `GET /customer/tasks` - List customer tasks
- `POST /tasks` - Create new task
- `GET /tasks/{id}` - Get task details
- `PUT /tasks/{id}` - Update task
- `DELETE /tasks/{id}` - Cancel task

#### Payments
- `POST /payments/process` - Process payment
- `GET /payments` - List payments
- `GET /payments/{id}` - Payment details
- `POST /payments/{id}/refund` - Request refund

### Work-Boy Mobile Application

#### Authentication
- `POST /auth/register` - Work-Boy registration
- `POST /auth/login` - Work-Boy login
- `POST /workboy/kyc` - Submit KYC documents
- `PUT /workboy/availability` - Update availability

#### Task Management
- `GET /workboy/tasks/available` - Get available tasks
- `GET /workboy/tasks` - Get assigned tasks
- `POST /workboy/tasks/{id}/accept` - Accept task
- `POST /workboy/tasks/{id}/reject` - Reject task
- `PUT /workboy/tasks/{id}/status` - Update task status
- `POST /workboy/tasks/{id}/complete` - Complete task

#### Earnings
- `GET /workboy/earnings` - Get earnings summary
- `GET /workboy/earnings/history` - Earnings history
- `POST /workboy/withdrawal` - Request withdrawal

### Admin Panel

#### Dashboard
- `GET /admin/dashboard` - Dashboard statistics
- `GET /admin/analytics` - Platform analytics

#### User Management
- `GET /admin/users` - List customers
- `GET /admin/users/{id}` - User details
- `PUT /admin/users/{id}` - Update user
- `POST /admin/users/{id}/suspend` - Suspend user

#### Work-Boy Management
- `GET /admin/workboys` - List Work-Boys
- `GET /admin/workboys/kyc` - KYC verification queue
- `POST /admin/workboys/{id}/kyc/approve` - Approve KYC
- `POST /admin/workboys/{id}/kyc/reject` - Reject KYC

#### Financial Management
- `GET /admin/payments` - List all payments
- `GET /admin/transactions` - Transaction history
- `GET /admin/reports/revenue` - Revenue reports

## WebSocket Integration

### Connection
```javascript
const ws = new WebSocket('ws://localhost:8080')

// Authenticate after connection
ws.send(JSON.stringify({
  type: 'authenticate',
  token: 'your_jwt_token'
}))
```

### Message Types

#### Client to Server
- `authenticate` - Authenticate connection
- `join_room` - Join specific room
- `location_update` - Send location update
- `task_update` - Update task status
- `ping` - Heartbeat

#### Server to Client
- `authenticated` - Authentication successful
- `task_update` - Task status changed
- `location_update` - Work-Boy location update
- `notification` - New notification
- `error` - Error message
- `pong` - Heartbeat response

### Real-time Features

#### Task Tracking
```javascript
// Track a specific task
websocket.send(JSON.stringify({
  type: 'join_room',
  room_id: `task_${taskId}`
}))

// Listen for updates
websocket.on('task_update', (data) => {
  console.log('Task updated:', data)
})
```

#### Location Tracking
```javascript
// Send location update (Work-Boy)
websocket.send(JSON.stringify({
  type: 'location_update',
  latitude: 40.7128,
  longitude: -74.0060
}))

// Receive location updates (Customer)
websocket.on('location_update', (data) => {
  updateMapMarker(data.latitude, data.longitude)
})
```

## Error Handling

### HTTP Status Codes
- `200` - Success
- `201` - Created
- `400` - Bad Request
- `401` - Unauthorized
- `403` - Forbidden
- `404` - Not Found
- `422` - Validation Error
- `429` - Rate Limited
- `500` - Server Error

### Error Response Format
```json
{
  "success": false,
  "message": "Error description",
  "errors": {
    "field_name": ["Validation error message"]
  },
  "code": "ERROR_CODE"
}
```

## Rate Limiting

- **Authentication endpoints**: 5 requests per minute
- **General API**: 100 requests per minute
- **WebSocket**: 1000 messages per minute

## Data Formats

### Date/Time
- All dates in ISO 8601 format: `2024-01-15T10:30:00Z`
- Timezone: UTC

### Coordinates
- Latitude/Longitude as decimal degrees
- Example: `{"latitude": 40.7128, "longitude": -74.0060}`

### Currency
- All amounts in smallest currency unit (paise for INR)
- Example: ₹10.50 = 1050

## Testing

### Integration Tests
```bash
# Run API integration tests
cd backend
php spark test --group integration

# Run with coverage
php spark test --group integration --coverage-html coverage/
```

### Postman Collection
Import the provided Postman collection for manual testing:
- `WorkBoy_API_Collection.json`

### Environment Variables
```env
# Development
API_BASE_URL=http://localhost:8080/api/v1
WS_URL=ws://localhost:8080

# Production
API_BASE_URL=https://api.workboy.com/api/v1
WS_URL=wss://ws.workboy.com
```

## Security Considerations

### API Security
- All endpoints use HTTPS in production
- JWT tokens for authentication
- Rate limiting to prevent abuse
- Input validation and sanitization
- CORS configuration for web clients

### Mobile Security
- Certificate pinning for API calls
- Secure token storage (Keychain/Keystore)
- Biometric authentication where available
- App transport security

### WebSocket Security
- WSS (WebSocket Secure) in production
- Token-based authentication
- Message validation
- Connection rate limiting

## Monitoring and Logging

### API Monitoring
- Response time tracking
- Error rate monitoring
- Rate limit violations
- Authentication failures

### WebSocket Monitoring
- Connection count
- Message throughput
- Reconnection rates
- Error frequencies

## Support

For integration support:
- Email: <EMAIL>
- Documentation: https://docs.workboy.com
- Status Page: https://status.workboy.com

## Changelog

### v1.0.0 (Current)
- Initial API release
- WebSocket real-time features
- Complete CRUD operations
- Payment integration
- Admin management APIs
