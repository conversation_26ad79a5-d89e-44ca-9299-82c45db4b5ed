import React, { createContext, useContext, useEffect, useState } from 'react'
import { apiService } from '@services/apiService'
import { authService } from '@services/authService'
import toast from 'react-hot-toast'

const AuthContext = createContext({})

export const useAuth = () => {
  const context = useContext(AuthContext)
  if (!context) {
    throw new Error('useAuth must be used within an AuthProvider')
  }
  return context
}

export const AuthProvider = ({ children }) => {
  const [user, setUser] = useState(null)
  const [loading, setLoading] = useState(true)
  const [isAuthenticated, setIsAuthenticated] = useState(false)

  useEffect(() => {
    checkAuthState()
  }, [])

  const checkAuthState = async () => {
    try {
      setLoading(true)
      const token = authService.getToken()
      
      if (token) {
        // Verify token with backend
        const response = await apiService.get('/admin/profile')
        if (response.success) {
          setUser(response.data)
          setIsAuthenticated(true)
        } else {
          // Token is invalid
          authService.removeToken()
          setUser(null)
          setIsAuthenticated(false)
        }
      }
    } catch (error) {
      console.error('Auth check failed:', error)
      authService.removeToken()
      setUser(null)
      setIsAuthenticated(false)
    } finally {
      setLoading(false)
    }
  }

  const login = async (email, password) => {
    try {
      setLoading(true)
      const response = await apiService.post('/admin/login', {
        email,
        password,
      })

      if (response.success) {
        const { token, user: userData } = response.data
        
        // Store token
        authService.setToken(token)
        
        // Set user data
        setUser(userData)
        setIsAuthenticated(true)
        
        toast.success('Welcome back!')
        return { success: true }
      } else {
        throw new Error(response.message || 'Login failed')
      }
    } catch (error) {
      console.error('Login error:', error)
      const message = error.response?.data?.message || error.message || 'Login failed'
      toast.error(message)
      throw error
    } finally {
      setLoading(false)
    }
  }

  const logout = async () => {
    try {
      // Call logout endpoint
      await apiService.post('/admin/logout')
    } catch (error) {
      console.error('Logout error:', error)
    } finally {
      // Clear local state regardless of API call result
      authService.removeToken()
      setUser(null)
      setIsAuthenticated(false)
      toast.success('Logged out successfully')
    }
  }

  const forgotPassword = async (email) => {
    try {
      const response = await apiService.post('/admin/forgot-password', {
        email,
      })

      if (response.success) {
        toast.success('Password reset instructions sent to your email')
        return { success: true }
      } else {
        throw new Error(response.message || 'Failed to send reset email')
      }
    } catch (error) {
      console.error('Forgot password error:', error)
      const message = error.response?.data?.message || error.message || 'Failed to send reset email'
      toast.error(message)
      throw error
    }
  }

  const updateProfile = async (profileData) => {
    try {
      setLoading(true)
      const response = await apiService.put('/admin/profile', profileData)

      if (response.success) {
        setUser(response.data)
        toast.success('Profile updated successfully')
        return { success: true }
      } else {
        throw new Error(response.message || 'Failed to update profile')
      }
    } catch (error) {
      console.error('Update profile error:', error)
      const message = error.response?.data?.message || error.message || 'Failed to update profile'
      toast.error(message)
      throw error
    } finally {
      setLoading(false)
    }
  }

  const changePassword = async (currentPassword, newPassword) => {
    try {
      setLoading(true)
      const response = await apiService.put('/admin/change-password', {
        current_password: currentPassword,
        new_password: newPassword,
      })

      if (response.success) {
        toast.success('Password changed successfully')
        return { success: true }
      } else {
        throw new Error(response.message || 'Failed to change password')
      }
    } catch (error) {
      console.error('Change password error:', error)
      const message = error.response?.data?.message || error.message || 'Failed to change password'
      toast.error(message)
      throw error
    } finally {
      setLoading(false)
    }
  }

  const refreshUser = async () => {
    try {
      const response = await apiService.get('/admin/profile')
      if (response.success) {
        setUser(response.data)
      }
    } catch (error) {
      console.error('Refresh user error:', error)
    }
  }

  const value = {
    // State
    user,
    loading,
    isAuthenticated,
    
    // Methods
    login,
    logout,
    forgotPassword,
    updateProfile,
    changePassword,
    refreshUser,
    
    // Computed values
    isAdmin: user?.role === 'admin',
    isSuperAdmin: user?.role === 'super_admin',
    canManageUsers: user?.permissions?.includes('manage_users') || user?.role === 'super_admin',
    canManageFinancials: user?.permissions?.includes('manage_financials') || user?.role === 'super_admin',
    canManageSettings: user?.permissions?.includes('manage_settings') || user?.role === 'super_admin',
  }

  return (
    <AuthContext.Provider value={value}>
      {children}
    </AuthContext.Provider>
  )
}
