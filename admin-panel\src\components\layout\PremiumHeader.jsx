import React, { useState } from 'react'
import { 
  Bars3Icon, 
  BellIcon, 
  MagnifyingGlassIcon,
  ChevronDownIcon,
  UserCircleIcon,
  Cog6ToothIcon,
  ArrowRightOnRectangleIcon,
  SunIcon,
  CommandLineIcon,
  SparklesIcon,
} from '@heroicons/react/24/outline'
import { Menu, Transition } from '@headlessui/react'
import { Fragment } from 'react'
import { useAuth } from '@context/AuthContext'
import { clsx } from 'clsx'

export const PremiumHeader = ({ onMenuClick }) => {
  const { user, logout } = useAuth()
  const [searchFocused, setSearchFocused] = useState(false)

  return (
    <header className="sticky top-0 z-30 bg-white/90 backdrop-blur-xl border-b border-secondary-200/50 shadow-sm">
      <div className="mx-auto max-w-7xl px-6 lg:px-8">
        <div className="flex h-16 justify-between items-center">
          {/* Mobile menu button */}
          <button
            type="button"
            className="lg:hidden p-2.5 rounded-xl text-secondary-500 hover:text-secondary-700 hover:bg-secondary-100 focus:outline-none focus:ring-2 focus:ring-primary-500 transition-all duration-200 hover:scale-110"
            onClick={onMenuClick}
          >
            <span className="sr-only">Open sidebar</span>
            <Bars3Icon className="h-6 w-6" aria-hidden="true" />
          </button>

          {/* Premium Search */}
          <div className="flex flex-1 justify-center lg:justify-start lg:max-w-md">
            <div className="w-full max-w-lg lg:max-w-xs">
              <label htmlFor="search" className="sr-only">Search</label>
              <div className={clsx(
                "relative transition-all duration-300",
                searchFocused ? "scale-105" : "scale-100"
              )}>
                <div className="pointer-events-none absolute inset-y-0 left-0 flex items-center pl-4">
                  <MagnifyingGlassIcon className={clsx(
                    "h-5 w-5 transition-colors duration-200",
                    searchFocused ? "text-primary-500" : "text-secondary-400"
                  )} />
                </div>
                <input
                  id="search"
                  name="search"
                  className="block w-full rounded-2xl border-0 bg-secondary-50/50 py-3 pl-12 pr-4 text-secondary-900 placeholder:text-secondary-400 focus:bg-white focus:ring-2 focus:ring-primary-500 focus:shadow-lg focus:shadow-primary-500/10 sm:text-sm transition-all duration-300 backdrop-blur-sm"
                  placeholder="Search anything..."
                  type="search"
                  onFocus={() => setSearchFocused(true)}
                  onBlur={() => setSearchFocused(false)}
                />
                <div className="pointer-events-none absolute inset-y-0 right-0 flex items-center pr-4">
                  <kbd className="hidden sm:inline-flex items-center rounded-lg border border-secondary-200 px-2 py-1 text-xs font-medium text-secondary-500">
                    ⌘K
                  </kbd>
                </div>
              </div>
            </div>
          </div>

          {/* Right side */}
          <div className="flex items-center space-x-3">
            {/* Quick Actions */}
            <div className="hidden md:flex items-center space-x-2">
              <button
                type="button"
                className="p-2.5 text-secondary-500 hover:text-secondary-700 hover:bg-secondary-100 rounded-xl transition-all duration-200 hover:scale-110"
                title="Command Palette"
              >
                <CommandLineIcon className="h-5 w-5" />
              </button>
              
              <button
                type="button"
                className="p-2.5 text-secondary-500 hover:text-secondary-700 hover:bg-secondary-100 rounded-xl transition-all duration-200 hover:scale-110"
                title="Toggle Theme"
              >
                <SunIcon className="h-5 w-5" />
              </button>
            </div>

            {/* Notifications */}
            <button
              type="button"
              className="relative p-2.5 text-secondary-500 hover:text-secondary-700 hover:bg-secondary-100 rounded-xl focus:outline-none focus:ring-2 focus:ring-primary-500 transition-all duration-200 hover:scale-110 group"
            >
              <span className="sr-only">View notifications</span>
              <BellIcon className="h-6 w-6" />
              <span className="absolute -top-1 -right-1 h-5 w-5 bg-gradient-to-r from-danger-400 to-danger-500 rounded-full flex items-center justify-center text-xs font-bold text-white shadow-lg animate-pulse">
                3
              </span>
            </button>

            {/* User menu */}
            <Menu as="div" className="relative">
              <Menu.Button className="flex items-center space-x-3 p-2 rounded-xl hover:bg-secondary-50 transition-all duration-200 hover:scale-105 group">
                <div className="relative">
                  <div className="h-10 w-10 rounded-xl bg-gradient-to-br from-primary-500 to-primary-600 flex items-center justify-center shadow-lg shadow-primary-500/25 transition-all duration-300 group-hover:scale-110">
                    <span className="text-sm font-bold text-white">
                      {user?.name?.charAt(0) || 'A'}
                    </span>
                  </div>
                  <div className="absolute -bottom-0.5 -right-0.5 h-3 w-3 bg-gradient-to-br from-success-400 to-success-500 rounded-full border-2 border-white"></div>
                </div>
                <div className="hidden md:block text-left">
                  <div className="text-sm font-semibold text-secondary-900">{user?.name || 'Admin User'}</div>
                  <div className="text-xs text-secondary-500 font-medium">{user?.role || 'Administrator'}</div>
                </div>
                <ChevronDownIcon className="h-4 w-4 text-secondary-400 group-hover:text-secondary-600 transition-colors duration-200" />
              </Menu.Button>

              <Transition
                as={Fragment}
                enter="transition ease-out duration-200"
                enterFrom="transform opacity-0 scale-95"
                enterTo="transform opacity-100 scale-100"
                leave="transition ease-in duration-150"
                leaveFrom="transform opacity-100 scale-100"
                leaveTo="transform opacity-0 scale-95"
              >
                <Menu.Items className="absolute right-0 z-50 mt-2 w-56 origin-top-right rounded-2xl bg-white/95 backdrop-blur-xl shadow-xl shadow-secondary-900/10 ring-1 ring-secondary-200/50 focus:outline-none">
                  <div className="p-2">
                    <div className="px-4 py-3 border-b border-secondary-200/50">
                      <p className="text-sm font-semibold text-secondary-900">{user?.name || 'Admin User'}</p>
                      <p className="text-xs text-secondary-500 truncate">{user?.email || '<EMAIL>'}</p>
                    </div>
                    
                    <div className="py-2 space-y-1">
                      <Menu.Item>
                        {({ active }) => (
                          <button
                            className={clsx(
                              'group flex w-full items-center rounded-xl px-4 py-2.5 text-sm font-medium transition-all duration-200',
                              active ? 'bg-secondary-100 text-secondary-900' : 'text-secondary-700'
                            )}
                          >
                            <UserCircleIcon className="mr-3 h-5 w-5" />
                            Profile Settings
                          </button>
                        )}
                      </Menu.Item>
                      
                      <Menu.Item>
                        {({ active }) => (
                          <button
                            className={clsx(
                              'group flex w-full items-center rounded-xl px-4 py-2.5 text-sm font-medium transition-all duration-200',
                              active ? 'bg-secondary-100 text-secondary-900' : 'text-secondary-700'
                            )}
                          >
                            <Cog6ToothIcon className="mr-3 h-5 w-5" />
                            Preferences
                          </button>
                        )}
                      </Menu.Item>
                      
                      <div className="border-t border-secondary-200/50 my-2"></div>
                      
                      <Menu.Item>
                        {({ active }) => (
                          <button
                            onClick={logout}
                            className={clsx(
                              'group flex w-full items-center rounded-xl px-4 py-2.5 text-sm font-medium transition-all duration-200',
                              active ? 'bg-danger-50 text-danger-700' : 'text-danger-600'
                            )}
                          >
                            <ArrowRightOnRectangleIcon className="mr-3 h-5 w-5" />
                            Sign Out
                          </button>
                        )}
                      </Menu.Item>
                    </div>
                  </div>
                </Menu.Items>
              </Transition>
            </Menu>
          </div>
        </div>
      </div>
    </header>
  )
}
