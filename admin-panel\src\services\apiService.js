import axios from 'axios'
import { authService } from './authService'
import toast from 'react-hot-toast'

// API Configuration
const API_BASE_URL = import.meta.env.VITE_API_BASE_URL || 'http://localhost:8080/api/v1'

// Create axios instance
const api = axios.create({
  baseURL: API_BASE_URL,
  timeout: 30000,
  headers: {
    'Content-Type': 'application/json',
  },
})

// Request interceptor to add auth token
api.interceptors.request.use(
  (config) => {
    const token = authService.getToken()
    if (token) {
      config.headers.Authorization = `Bearer ${token}`
    }
    return config
  },
  (error) => {
    return Promise.reject(error)
  }
)

// Response interceptor to handle errors
api.interceptors.response.use(
  (response) => {
    // Return the data directly for successful responses
    return response.data
  },
  (error) => {
    console.error('API Error:', error)
    
    // Handle different error scenarios
    if (error.response) {
      // Server responded with error status
      const { status, data } = error.response
      
      switch (status) {
        case 401:
          // Unauthorized - clear auth and redirect to login
          authService.removeToken()
          window.location.href = '/login'
          break
          
        case 403:
          toast.error('Access denied. You don\'t have permission to perform this action.')
          break
          
        case 404:
          toast.error('Resource not found.')
          break
          
        case 422:
          // Validation errors
          if (data.errors) {
            const errorMessages = Object.values(data.errors).flat()
            errorMessages.forEach(message => toast.error(message))
          } else {
            toast.error(data.message || 'Validation error occurred.')
          }
          break
          
        case 429:
          toast.error('Too many requests. Please try again later.')
          break
          
        case 500:
          toast.error('Server error. Please try again later.')
          break
          
        default:
          toast.error(data.message || 'An unexpected error occurred.')
      }
      
      return Promise.reject(error.response.data)
    } else if (error.request) {
      // Network error
      toast.error('Network error. Please check your connection.')
      return Promise.reject({ message: 'Network error' })
    } else {
      // Other error
      toast.error('An unexpected error occurred.')
      return Promise.reject({ message: error.message })
    }
  }
)

// API endpoints
export const endpoints = {
  // Admin Authentication
  admin: {
    login: '/admin/login',
    logout: '/admin/logout',
    profile: '/admin/profile',
    forgotPassword: '/admin/forgot-password',
    changePassword: '/admin/change-password',
    dashboard: '/admin/dashboard',
  },
  
  // User Management
  users: {
    list: '/admin/users',
    detail: (id) => `/admin/users/${id}`,
    create: '/admin/users',
    update: (id) => `/admin/users/${id}`,
    delete: (id) => `/admin/users/${id}`,
    suspend: (id) => `/admin/users/${id}/suspend`,
    activate: (id) => `/admin/users/${id}/activate`,
  },
  
  // Work-Boy Management
  workboys: {
    list: '/admin/workboys',
    detail: (id) => `/admin/workboys/${id}`,
    update: (id) => `/admin/workboys/${id}`,
    suspend: (id) => `/admin/workboys/${id}/suspend`,
    activate: (id) => `/admin/workboys/${id}/activate`,
    kyc: '/admin/workboys/kyc',
    approveKyc: (id) => `/admin/workboys/${id}/kyc/approve`,
    rejectKyc: (id) => `/admin/workboys/${id}/kyc/reject`,
  },
  
  // Task Management
  tasks: {
    list: '/admin/tasks',
    detail: (id) => `/admin/tasks/${id}`,
    update: (id) => `/admin/tasks/${id}`,
    cancel: (id) => `/admin/tasks/${id}/cancel`,
    categories: '/admin/categories',
    createCategory: '/admin/categories',
    updateCategory: (id) => `/admin/categories/${id}`,
    deleteCategory: (id) => `/admin/categories/${id}`,
  },
  
  // Financial Management
  payments: {
    list: '/admin/payments',
    detail: (id) => `/admin/payments/${id}`,
    refund: (id) => `/admin/payments/${id}/refund`,
  },
  
  transactions: {
    list: '/admin/transactions',
    detail: (id) => `/admin/transactions/${id}`,
  },
  
  reports: {
    revenue: '/admin/reports/revenue',
    users: '/admin/reports/users',
    tasks: '/admin/reports/tasks',
    export: '/admin/reports/export',
  },
  
  // Settings
  settings: {
    get: '/admin/settings',
    update: '/admin/settings',
    adminUsers: '/admin/admin-users',
    createAdminUser: '/admin/admin-users',
    updateAdminUser: (id) => `/admin/admin-users/${id}`,
    deleteAdminUser: (id) => `/admin/admin-users/${id}`,
  },
}

// API service methods
export const apiService = {
  // GET request
  get: async (url, config = {}) => {
    try {
      return await api.get(url, config)
    } catch (error) {
      throw error
    }
  },
  
  // POST request
  post: async (url, data = {}, config = {}) => {
    try {
      return await api.post(url, data, config)
    } catch (error) {
      throw error
    }
  },
  
  // PUT request
  put: async (url, data = {}, config = {}) => {
    try {
      return await api.put(url, data, config)
    } catch (error) {
      throw error
    }
  },
  
  // DELETE request
  delete: async (url, config = {}) => {
    try {
      return await api.delete(url, config)
    } catch (error) {
      throw error
    }
  },
  
  // Upload file
  uploadFile: async (url, file, onProgress = null) => {
    const formData = new FormData()
    formData.append('file', file)
    
    try {
      return await api.post(url, formData, {
        headers: {
          'Content-Type': 'multipart/form-data',
        },
        onUploadProgress: (progressEvent) => {
          if (onProgress) {
            const percentCompleted = Math.round(
              (progressEvent.loaded * 100) / progressEvent.total
            )
            onProgress(percentCompleted)
          }
        },
      })
    } catch (error) {
      throw error
    }
  },
  
  // Upload multiple files
  uploadFiles: async (url, files, onProgress = null) => {
    const formData = new FormData()
    
    files.forEach((file, index) => {
      formData.append(`files[${index}]`, file)
    })
    
    try {
      return await api.post(url, formData, {
        headers: {
          'Content-Type': 'multipart/form-data',
        },
        onUploadProgress: (progressEvent) => {
          if (onProgress) {
            const percentCompleted = Math.round(
              (progressEvent.loaded * 100) / progressEvent.total
            )
            onProgress(percentCompleted)
          }
        },
      })
    } catch (error) {
      throw error
    }
  },
  
  // Download file
  downloadFile: async (url, filename) => {
    try {
      const response = await api.get(url, {
        responseType: 'blob',
      })
      
      // Create blob link to download
      const blob = new Blob([response])
      const link = document.createElement('a')
      link.href = window.URL.createObjectURL(blob)
      link.download = filename
      link.click()
      
      return { success: true }
    } catch (error) {
      throw error
    }
  },
}

export default apiService
