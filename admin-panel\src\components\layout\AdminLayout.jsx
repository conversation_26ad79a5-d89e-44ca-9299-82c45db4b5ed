import React, { useState } from 'react'
import { Outlet } from 'react-router-dom'
import { Helmet } from 'react-helmet-async'
import { PremiumSidebar } from './PremiumSidebar'
import { PremiumHeader } from './PremiumHeader'

export const AdminLayout = ({ children }) => {
  const [sidebarOpen, setSidebarOpen] = useState(false)

  return (
    <div className="min-h-screen bg-gradient-to-br from-secondary-50 via-white to-primary-50/30 relative">
      <Helmet>
        <title>Admin Dashboard - WorkBoy</title>
      </Helmet>

      {/* Premium Background Pattern */}
      <div className="fixed inset-0 opacity-30 pointer-events-none">
        <div className="absolute inset-0 bg-gradient-to-br from-primary-50/20 via-transparent to-accent-50/20"></div>
      </div>

      {/* Premium Sidebar */}
      <PremiumSidebar open={sidebarOpen} setOpen={setSidebarOpen} />

      {/* Main content */}
      <div className="lg:pl-72 relative">
        {/* Premium Header */}
        <PremiumHeader onMenuClick={() => setSidebarOpen(true)} />

        {/* Page content */}
        <main className="relative">
          <div className="mx-auto max-w-7xl px-6 py-8 lg:px-8">
            {children || <Outlet />}
          </div>
        </main>
      </div>
    </div>
  )
}
