import React, { Fragment, useState } from 'react'
import { Link, useLocation } from 'react-router-dom'
import { Dialog, Transition } from '@headlessui/react'
import { XMarkIcon } from '@heroicons/react/24/outline'
import {
  HomeIcon,
  UsersIcon,
  ClipboardDocumentListIcon,
  CreditCardIcon,
  ChartBarIcon,
  CogIcon,
  UserGroupIcon,
  DocumentCheckIcon,
  TagIcon,
  BanknotesIcon,
  SparklesIcon,
  BoltIcon,
  ShieldCheckIcon,
  ChevronDownIcon,
  ChevronRightIcon,
} from '@heroicons/react/24/outline'
import { useAuth } from '@context/AuthContext'
import { clsx } from 'clsx'

const navigation = [
  { 
    name: 'Dashboard', 
    href: '/dashboard', 
    icon: HomeIcon,
    badge: null,
    gradient: 'from-primary-500 to-primary-600'
  },
  {
    name: 'User Management',
    icon: UsersIcon,
    gradient: 'from-blue-500 to-blue-600',
    children: [
      { name: 'Customers', href: '/users', icon: UsersIcon },
      { name: 'Work-Boys', href: '/workboys', icon: UserGroupIcon },
      { name: 'KYC Verification', href: '/kyc-verification', icon: ShieldCheckIcon, badge: '3' },
    ],
  },
  {
    name: 'Task Management',
    icon: ClipboardDocumentListIcon,
    gradient: 'from-emerald-500 to-emerald-600',
    children: [
      { name: 'All Tasks', href: '/tasks', icon: ClipboardDocumentListIcon },
      { name: 'Categories', href: '/categories', icon: TagIcon },
    ],
  },
  {
    name: 'Financial',
    icon: CreditCardIcon,
    gradient: 'from-amber-500 to-amber-600',
    children: [
      { name: 'Payments', href: '/payments', icon: CreditCardIcon },
      { name: 'Transactions', href: '/transactions', icon: BanknotesIcon },
      { name: 'Reports', href: '/reports', icon: ChartBarIcon },
    ],
  },
  { 
    name: 'Analytics', 
    href: '/analytics', 
    icon: ChartBarIcon,
    gradient: 'from-purple-500 to-purple-600',
    badge: 'Pro'
  },
  {
    name: 'Settings',
    icon: CogIcon,
    gradient: 'from-secondary-500 to-secondary-600',
    children: [
      { name: 'General Settings', href: '/settings', icon: CogIcon },
      { name: 'Admin Users', href: '/admin-users', icon: ShieldCheckIcon },
    ],
  },
]

const NavigationItem = ({ item, currentPath }) => {
  const [isOpen, setIsOpen] = useState(false)
  const isActive = (href) => currentPath === href
  const hasActiveChild = item.children?.some(child => isActive(child.href))

  if (!item.children) {
    return (
      <Link
        to={item.href}
        className={clsx(
          'group flex items-center justify-between px-4 py-3 text-sm font-medium rounded-xl transition-all duration-300 hover:scale-[1.02]',
          isActive(item.href)
            ? 'bg-gradient-to-r from-primary-500 to-primary-600 text-white shadow-lg shadow-primary-500/25 transform scale-[1.02]'
            : 'text-secondary-700 hover:bg-secondary-50 hover:text-secondary-900'
        )}
      >
        <div className="flex items-center space-x-3">
          <div className={clsx(
            'p-2 rounded-lg transition-all duration-300',
            isActive(item.href)
              ? 'bg-white/20 shadow-sm'
              : 'bg-secondary-100 group-hover:bg-secondary-200 group-hover:scale-110'
          )}>
            <item.icon className="h-5 w-5" />
          </div>
          <span className="font-semibold">{item.name}</span>
        </div>
        {item.badge && (
          <span className={clsx(
            'px-2.5 py-1 text-xs font-bold rounded-full transition-all duration-300',
            isActive(item.href)
              ? 'bg-white/20 text-white'
              : item.badge === 'Pro' 
                ? 'bg-gradient-to-r from-amber-400 to-amber-500 text-white shadow-sm'
                : 'bg-primary-100 text-primary-700'
          )}>
            {item.badge}
          </span>
        )}
      </Link>
    )
  }

  return (
    <div className="space-y-2">
      <button
        onClick={() => setIsOpen(!isOpen)}
        className={clsx(
          'w-full group flex items-center justify-between px-4 py-3 text-sm font-medium rounded-xl transition-all duration-300 hover:scale-[1.01]',
          hasActiveChild || isOpen
            ? 'bg-secondary-100 text-secondary-900'
            : 'text-secondary-700 hover:bg-secondary-50 hover:text-secondary-900'
        )}
      >
        <div className="flex items-center space-x-3">
          <div className={`p-2 rounded-lg bg-gradient-to-r ${item.gradient} shadow-sm transition-all duration-300 group-hover:scale-110`}>
            <item.icon className="h-5 w-5 text-white" />
          </div>
          <span className="font-semibold">{item.name}</span>
        </div>
        <ChevronDownIcon className={clsx(
          'h-4 w-4 transition-transform duration-300',
          isOpen ? 'rotate-180' : 'rotate-0'
        )} />
      </button>
      
      <Transition
        show={isOpen}
        enter="transition-all duration-300 ease-out"
        enterFrom="opacity-0 max-h-0"
        enterTo="opacity-100 max-h-96"
        leave="transition-all duration-300 ease-in"
        leaveFrom="opacity-100 max-h-96"
        leaveTo="opacity-0 max-h-0"
      >
        <div className="ml-6 space-y-1 overflow-hidden">
          {item.children.map((subItem) => (
            <Link
              key={subItem.name}
              to={subItem.href}
              className={clsx(
                'group flex items-center justify-between px-4 py-2.5 text-sm font-medium rounded-lg transition-all duration-300 hover:scale-[1.01]',
                isActive(subItem.href)
                  ? 'bg-gradient-to-r from-primary-50 to-primary-100 text-primary-700 border-l-4 border-primary-500 shadow-sm'
                  : 'text-secondary-600 hover:bg-secondary-50 hover:text-secondary-900'
              )}
            >
              <div className="flex items-center space-x-3">
                {subItem.icon && (
                  <div className={clsx(
                    'p-1.5 rounded-md transition-all duration-300',
                    isActive(subItem.href) 
                      ? 'bg-primary-200 text-primary-700' 
                      : 'bg-secondary-200 text-secondary-500 group-hover:bg-secondary-300'
                  )}>
                    <subItem.icon className="h-4 w-4" />
                  </div>
                )}
                <span>{subItem.name}</span>
              </div>
              {subItem.badge && (
                <span className="px-2 py-0.5 text-xs font-bold bg-gradient-to-r from-danger-400 to-danger-500 text-white rounded-full shadow-sm animate-pulse">
                  {subItem.badge}
                </span>
              )}
            </Link>
          ))}
        </div>
      </Transition>
    </div>
  )
}

export const PremiumSidebar = ({ open, setOpen }) => {
  const location = useLocation()
  const { user, logout } = useAuth()

  const SidebarContent = () => (
    <div className="flex h-full flex-col bg-white/95 backdrop-blur-xl border-r border-secondary-200/50 shadow-xl">
      {/* Premium Logo Section */}
      <div className="flex h-20 shrink-0 items-center px-6 border-b border-secondary-200/50 bg-gradient-to-r from-white to-secondary-50/50">
        <div className="flex items-center space-x-3">
          <div className="relative group">
            <div className="h-12 w-12 rounded-2xl bg-gradient-to-br from-primary-500 via-primary-600 to-primary-700 flex items-center justify-center shadow-lg shadow-primary-500/25 transition-all duration-300 group-hover:scale-110 group-hover:shadow-xl group-hover:shadow-primary-500/40">
              <BoltIcon className="h-7 w-7 text-white" />
            </div>
            <div className="absolute -top-1 -right-1 h-5 w-5 bg-gradient-to-br from-amber-400 to-amber-500 rounded-full flex items-center justify-center shadow-sm animate-pulse">
              <SparklesIcon className="h-3 w-3 text-white" />
            </div>
          </div>
          <div>
            <h1 className="text-xl font-bold bg-gradient-to-r from-primary-600 via-primary-700 to-primary-800 bg-clip-text text-transparent">
              WorkBoy
            </h1>
            <p className="text-xs text-secondary-500 font-medium tracking-wide">Admin Panel</p>
          </div>
        </div>
      </div>

      {/* Premium Navigation */}
      <nav className="flex flex-1 flex-col px-4 py-6 space-y-2 overflow-y-auto">
        {navigation.map((item) => (
          <NavigationItem
            key={item.name}
            item={item}
            currentPath={location.pathname}
          />
        ))}
      </nav>

      {/* Premium User Section */}
      <div className="border-t border-secondary-200/50 p-4 bg-gradient-to-r from-white to-secondary-50/30">
        <div className="flex items-center space-x-3 p-3 rounded-2xl bg-gradient-to-r from-secondary-50 to-secondary-100/50 hover:from-secondary-100 hover:to-secondary-150 transition-all duration-300 cursor-pointer group hover:scale-[1.02] hover:shadow-lg">
          <div className="relative">
            <div className="h-11 w-11 rounded-xl bg-gradient-to-br from-primary-500 to-primary-600 flex items-center justify-center shadow-lg shadow-primary-500/25 transition-all duration-300 group-hover:scale-110">
              <span className="text-sm font-bold text-white">
                {user?.name?.charAt(0) || 'A'}
              </span>
            </div>
            <div className="absolute -bottom-0.5 -right-0.5 h-3.5 w-3.5 bg-gradient-to-br from-success-400 to-success-500 rounded-full border-2 border-white shadow-sm"></div>
          </div>
          <div className="flex-1 min-w-0">
            <p className="text-sm font-bold text-secondary-900 truncate">
              {user?.name || 'Admin User'}
            </p>
            <p className="text-xs text-secondary-500 truncate font-medium">
              {user?.email || '<EMAIL>'}
            </p>
          </div>
          <button
            onClick={logout}
            className="p-2.5 rounded-xl text-secondary-400 hover:text-secondary-600 hover:bg-secondary-200 transition-all duration-300 hover:scale-110"
            title="Sign out"
          >
            <svg className="h-4 w-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M17 16l4-4m0 0l-4-4m4 4H7m6 4v1a3 3 0 01-3 3H6a3 3 0 01-3-3V7a3 3 0 013-3h4a3 3 0 013 3v1" />
            </svg>
          </button>
        </div>
      </div>
    </div>
  )

  return (
    <>
      {/* Mobile sidebar */}
      <Transition.Root show={open} as={Fragment}>
        <Dialog as="div" className="relative z-50 lg:hidden" onClose={setOpen}>
          <Transition.Child
            as={Fragment}
            enter="transition-opacity ease-linear duration-300"
            enterFrom="opacity-0"
            enterTo="opacity-100"
            leave="transition-opacity ease-linear duration-300"
            leaveFrom="opacity-100"
            leaveTo="opacity-0"
          >
            <div className="fixed inset-0 bg-secondary-900/80 backdrop-blur-sm" />
          </Transition.Child>

          <div className="fixed inset-0 flex">
            <Transition.Child
              as={Fragment}
              enter="transition ease-in-out duration-300 transform"
              enterFrom="-translate-x-full"
              enterTo="translate-x-0"
              leave="transition ease-in-out duration-300 transform"
              leaveFrom="translate-x-0"
              leaveTo="-translate-x-full"
            >
              <Dialog.Panel className="relative mr-16 flex w-full max-w-xs flex-1">
                <Transition.Child
                  as={Fragment}
                  enter="ease-in-out duration-300"
                  enterFrom="opacity-0"
                  enterTo="opacity-100"
                  leave="ease-in-out duration-300"
                  leaveFrom="opacity-100"
                  leaveTo="opacity-0"
                >
                  <div className="absolute left-full top-0 flex w-16 justify-center pt-5">
                    <button
                      type="button"
                      className="p-2.5 rounded-xl bg-white/10 backdrop-blur-sm text-white hover:bg-white/20 transition-all duration-300"
                      onClick={() => setOpen(false)}
                    >
                      <span className="sr-only">Close sidebar</span>
                      <XMarkIcon className="h-6 w-6" aria-hidden="true" />
                    </button>
                  </div>
                </Transition.Child>
                <SidebarContent />
              </Dialog.Panel>
            </Transition.Child>
          </div>
        </Dialog>
      </Transition.Root>

      {/* Static sidebar for desktop */}
      <div className="hidden lg:fixed lg:inset-y-0 lg:z-40 lg:flex lg:w-72 lg:flex-col">
        <SidebarContent />
      </div>
    </>
  )
}
